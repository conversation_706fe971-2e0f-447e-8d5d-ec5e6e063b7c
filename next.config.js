/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    // Explicitly load environment variables
    AZURE_OPENAI_API_KEY: process.env.AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_ENDPOINT: process.env.AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_API_VERSION: process.env.AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
    AZURE_OPENAI_VISION_DEPLOYMENT_NAME: process.env.AZURE_OPENAI_VISION_DEPLOYMENT_NAME,
  },
  experimental: {
    // Enable server components
    serverComponentsExternalPackages: ['@azure/openai'],
  },
}

module.exports = nextConfig
