-- Add sharing columns to trades table
ALTER TABLE public.trades 
ADD COLUMN IF NOT EXISTS is_shared BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS share_token TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS shared_at TIMESTAMP WITH TIME ZONE;

-- Create index for share tokens
CREATE INDEX IF NOT EXISTS trades_share_token_idx ON public.trades(share_token) WHERE share_token IS NOT NULL;

-- Remove the function if it exists (we'll generate tokens in Node.js instead)
DROP FUNCTION IF EXISTS generate_share_token();
