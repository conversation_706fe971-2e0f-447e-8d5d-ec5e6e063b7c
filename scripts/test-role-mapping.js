const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testRoleMapping() {
	try {
		console.log("Testing role mapping fix...");

		const testUserId = "a1d3aa48-1033-428d-99c2-f1c3b25df801"; // Real user ID

		// Create a test conversation
		const { data: conversation, error: convError } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: testUserId,
				title: "Role Mapping Test",
			})
			.select()
			.single();

		if (convError) {
			console.error("❌ Error creating conversation:", convError);
			return;
		}
		console.log("✅ Conversation created:", conversation.id);

		// Test the role mapping by simulating what our service does
		const roleMapping = {
			user: "user",
			ai: "assistant", // This should map 'ai' to 'assistant'
			system: "system",
		};

		// Test user message
		console.log("1. Testing user message...");
		const { data: userMsg, error: userError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: roleMapping["user"],
				content: "Hello from user",
				metadata: { type: "user" },
			})
			.select()
			.single();

		if (userError) {
			console.error("❌ User message failed:", userError);
		} else {
			console.log("✅ User message created:", userMsg.id);
		}

		// Test AI message (this was failing before)
		console.log("2. Testing AI message...");
		const { data: aiMsg, error: aiError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: roleMapping["ai"], // Should be 'assistant'
				content: "Hello from AI",
				metadata: { type: "ai" },
			})
			.select()
			.single();

		if (aiError) {
			console.error("❌ AI message failed:", aiError);
		} else {
			console.log("✅ AI message created:", aiMsg.id);
		}

		// Test system message
		console.log("3. Testing system message...");
		const { data: sysMsg, error: sysError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: roleMapping["system"],
				content: "System message",
				metadata: { type: "system" },
			})
			.select()
			.single();

		if (sysError) {
			console.error("❌ System message failed:", sysError);
		} else {
			console.log("✅ System message created:", sysMsg.id);
		}

		// Test retrieving and mapping back
		console.log("4. Testing retrieval and reverse mapping...");
		const { data: messages, error: getError } = await supabase
			.from("chat_messages")
			.select("*")
			.eq("conversation_id", conversation.id)
			.order("created_at", { ascending: true });

		if (getError) {
			console.error("❌ Error getting messages:", getError);
		} else {
			console.log("✅ Retrieved messages:");
			const typeMapping = {
				user: "user",
				assistant: "ai", // Map back to 'ai'
				system: "system",
			};

			messages.forEach((msg, index) => {
				const mappedType = typeMapping[msg.role] || "user";
				console.log(`  ${index + 1}. DB role: '${msg.role}' → Interface type: '${mappedType}' | Content: ${msg.content}`);
			});
		}

		// Clean up
		await supabase
			.from("chat_conversations")
			.delete()
			.eq("id", conversation.id);
		console.log("✅ Test data cleaned up");

		console.log("\n🎉 Role mapping test completed successfully!");
	} catch (error) {
		console.error("Test failed:", error);
	}
}

testRoleMapping();
