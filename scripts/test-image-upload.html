<!DOCTYPE html>
<html>
<head>
    <title>Test Image Upload Base64 Conversion</title>
</head>
<body>
    <h1>Test Image Upload Base64 Conversion</h1>
    <input type="file" id="fileInput" accept="image/*">
    <div id="result"></div>
    
    <script>
        // Helper function to convert file to base64
        const fileToBase64 = (file) => {
            return new Promise((resolve, reject) => {
                const reader = new FileReader()
                reader.readAsDataURL(file)
                reader.onload = () => resolve(reader.result)
                reader.onerror = error => reject(error)
            })
        }

        document.getElementById('fileInput').addEventListener('change', async (e) => {
            const file = e.target.files[0]
            if (!file) return

            const resultDiv = document.getElementById('result')
            
            try {
                // Test blob URL (what was causing the issue)
                const blobUrl = URL.createObjectURL(file)
                console.log('Blob URL:', blobUrl)
                
                // Test base64 conversion (the fix)
                const base64Url = await fileToBase64(file)
                console.log('Base64 URL:', base64Url.substring(0, 100) + '...')
                
                resultDiv.innerHTML = `
                    <h3>File: ${file.name}</h3>
                    <p><strong>Type:</strong> ${file.type}</p>
                    <p><strong>Size:</strong> ${file.size} bytes</p>
                    <p><strong>Blob URL (❌ Won't work with Azure OpenAI):</strong><br><code>${blobUrl}</code></p>
                    <p><strong>Base64 URL (✅ Will work with Azure OpenAI):</strong><br><code>${base64Url.substring(0, 100)}...</code></p>
                    <img src="${base64Url}" style="max-width: 200px; max-height: 200px;" alt="Preview">
                `
                
                // Test if it's a valid data URL
                const isValidDataUrl = base64Url.startsWith('data:image/')
                console.log('Is valid data URL:', isValidDataUrl)
                
            } catch (error) {
                console.error('Error:', error)
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`
            }
        })
    </script>
</body>
</html>
