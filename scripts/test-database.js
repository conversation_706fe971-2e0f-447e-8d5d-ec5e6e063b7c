const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDatabase() {
	try {
		console.log("Testing database tables...");

		// Test chat_conversations table
		console.log("1. Testing chat_conversations table...");
		const { data: conversations, error: convError } = await supabase
			.from("chat_conversations")
			.select("*")
			.limit(1);

		if (convError) {
			console.error("❌ chat_conversations table error:", convError);
		} else {
			console.log("✅ chat_conversations table exists");
		}

		// Test chat_messages table
		console.log("2. Testing chat_messages table...");
		const { data: messages, error: msgError } = await supabase
			.from("chat_messages")
			.select("*")
			.limit(1);

		if (msgError) {
			console.error("❌ chat_messages table error:", msgError);
		} else {
			console.log("✅ chat_messages table exists");
		}

		// Test chat_file_attachments table
		console.log("3. Testing chat_file_attachments table...");
		const { data: attachments, error: attError } = await supabase
			.from("chat_file_attachments")
			.select("*")
			.limit(1);

		if (attError) {
			console.error("❌ chat_file_attachments table error:", attError);
		} else {
			console.log("✅ chat_file_attachments table exists");
		}

		// Test creating a conversation with metadata
		console.log("4. Testing conversation creation with metadata...");
		const testUserId = "00000000-0000-0000-0000-000000000000"; // Test UUID
		const { data: newConv, error: createError } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: testUserId,
				title: "Test Conversation",
				metadata: { test: true, created: new Date().toISOString() },
			})
			.select()
			.single();

		if (createError) {
			console.error("❌ Error creating conversation:", createError);
		} else {
			console.log("✅ Successfully created conversation with metadata:", newConv.id);
			
			// Clean up - delete the test conversation
			await supabase
				.from("chat_conversations")
				.delete()
				.eq("id", newConv.id);
			console.log("✅ Test conversation cleaned up");
		}

		console.log("\nDatabase test completed!");
	} catch (error) {
		console.error("Test failed:", error);
	}
}

testDatabase();
