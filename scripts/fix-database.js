const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixDatabase() {
	try {
		console.log("Fixing database schema...");

		// First, let's check the current structure of chat_conversations
		console.log("1. Checking chat_conversations structure...");
		const { data: convData, error: convError } = await supabase
			.from("chat_conversations")
			.select("*")
			.limit(1);

		if (convError) {
			console.error("Error checking conversations:", convError);
		} else {
			console.log("Current conversation data sample:", convData);
		}

		// Try to add metadata column to chat_conversations if it doesn't exist
		console.log("2. Adding metadata column to chat_conversations...");
		try {
			// Use a direct SQL query through the REST API
			const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${supabaseServiceKey}`,
					'apikey': supabaseServiceKey
				},
				body: JSON.stringify({
					sql: "ALTER TABLE chat_conversations ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;"
				})
			});

			if (response.ok) {
				console.log("✅ Added metadata column to chat_conversations");
			} else {
				console.log("⚠️ Could not add metadata column via REST API");
			}
		} catch (err) {
			console.log("⚠️ Could not add metadata column:", err.message);
		}

		// Create chat_file_attachments table
		console.log("3. Creating chat_file_attachments table...");
		try {
			const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${supabaseServiceKey}`,
					'apikey': supabaseServiceKey
				},
				body: JSON.stringify({
					sql: `CREATE TABLE IF NOT EXISTS chat_file_attachments (
						id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
						message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
						file_name TEXT NOT NULL,
						file_type TEXT NOT NULL,
						file_size BIGINT NOT NULL,
						storage_path TEXT NOT NULL,
						preview_url TEXT,
						content_extracted TEXT,
						created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
						metadata JSONB DEFAULT '{}'::jsonb
					);`
				})
			});

			if (response.ok) {
				console.log("✅ Created chat_file_attachments table");
			} else {
				console.log("⚠️ Could not create chat_file_attachments table via REST API");
			}
		} catch (err) {
			console.log("⚠️ Could not create chat_file_attachments table:", err.message);
		}

		// Test the fix
		console.log("4. Testing the fixes...");
		const testUserId = "00000000-0000-0000-0000-000000000000"; // Test UUID
		const { data: newConv, error: createError } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: testUserId,
				title: "Test Conversation",
				metadata: { test: true, created: new Date().toISOString() },
			})
			.select()
			.single();

		if (createError) {
			console.error("❌ Still error creating conversation:", createError);
		} else {
			console.log("✅ Successfully created conversation with metadata:", newConv.id);
			
			// Clean up - delete the test conversation
			await supabase
				.from("chat_conversations")
				.delete()
				.eq("id", newConv.id);
			console.log("✅ Test conversation cleaned up");
		}

		console.log("\nDatabase fix completed!");
	} catch (error) {
		console.error("Fix failed:", error);
	}
}

fixDatabase();
