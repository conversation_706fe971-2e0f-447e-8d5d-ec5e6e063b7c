-- SQL commands to run in your Supabase SQL Editor
-- Go to: https://supabase.com/dashboard/project/znkjypzbvlykdmstyeux/sql/new

-- 1. Add missing columns to chat_conversations table
ALTER TABLE chat_conversations 
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- 2. Add missing columns to chat_messages table
ALTER TABLE chat_messages 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS is_streaming BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS analysis JSONB DEFAULT NULL;

-- 3. Create chat_file_attachments table
CREATE TABLE IF NOT EXISTS chat_file_attachments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  storage_path TEXT NOT NULL,
  preview_url TEXT,
  content_extracted TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chat_conversations_metadata ON chat_conversations USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_chat_messages_updated_at ON chat_messages(updated_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_analysis ON chat_messages USING GIN(analysis);
CREATE INDEX IF NOT EXISTS idx_chat_file_attachments_message_id ON chat_file_attachments(message_id);

-- 5. Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. Create triggers for updated_at
DROP TRIGGER IF EXISTS update_chat_conversations_updated_at ON chat_conversations;
CREATE TRIGGER update_chat_conversations_updated_at 
  BEFORE UPDATE ON chat_conversations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_chat_messages_updated_at ON chat_messages;
CREATE TRIGGER update_chat_messages_updated_at 
  BEFORE UPDATE ON chat_messages 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Enable Row Level Security (RLS) on new table
ALTER TABLE chat_file_attachments ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for chat_file_attachments
CREATE POLICY "Users can view file attachments in their messages" ON chat_file_attachments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM chat_messages cm
      JOIN chat_conversations cc ON cm.conversation_id = cc.id
      WHERE cm.id = message_id AND cc.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert file attachments in their messages" ON chat_file_attachments
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM chat_messages cm
      JOIN chat_conversations cc ON cm.conversation_id = cc.id
      WHERE cm.id = message_id AND cc.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update file attachments in their messages" ON chat_file_attachments
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM chat_messages cm
      JOIN chat_conversations cc ON cm.conversation_id = cc.id
      WHERE cm.id = message_id AND cc.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete file attachments in their messages" ON chat_file_attachments
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM chat_messages cm
      JOIN chat_conversations cc ON cm.conversation_id = cc.id
      WHERE cm.id = message_id AND cc.user_id = auth.uid()
    )
  );
