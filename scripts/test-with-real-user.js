const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testWithRealUser() {
	try {
		console.log("Testing chat creation with real user...");

		// 1. Check existing conversations to get a real user_id
		console.log("1. Getting existing user ID...");
		const { data: existingConv, error: convError } = await supabase
			.from("chat_conversations")
			.select("user_id")
			.limit(1);

		if (convError || !existingConv || existingConv.length === 0) {
			console.log("❌ No existing conversations found. Need to create a user first.");
			console.log("Please sign up in your app first to create a user, then try again.");
			return;
		}

		const realUserId = existingConv[0].user_id;
		console.log("✅ Found real user ID:", realUserId);

		// 2. Test conversation creation
		console.log("2. Creating conversation...");
		const { data: conversation, error: createConvError } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: realUserId,
				title: "Test Conversation",
			})
			.select()
			.single();

		if (createConvError) {
			console.error("❌ Error creating conversation:", createConvError);
			return;
		}
		console.log("✅ Conversation created:", conversation.id);

		// 3. Test message creation
		console.log("3. Creating message...");
		const { data: message, error: msgError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: "user", // Using 'role' column as it exists in DB
				content: "Hello, this is a test message",
				metadata: { test: true },
			})
			.select()
			.single();

		if (msgError) {
			console.error("❌ Error creating message:", msgError);
		} else {
			console.log("✅ Message created:", message.id);
			console.log("Message data:", message);
		}

		// 4. Test AI response message
		console.log("4. Creating AI response...");
		const { data: aiMessage, error: aiError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: "assistant",
				content: "Hello! This is a test AI response.",
				metadata: { model: "gpt-4o-mini" },
			})
			.select()
			.single();

		if (aiError) {
			console.error("❌ Error creating AI message:", aiError);
		} else {
			console.log("✅ AI message created:", aiMessage.id);
		}

		// 5. Test retrieving conversation messages
		console.log("5. Retrieving conversation messages...");
		const { data: messages, error: getError } = await supabase
			.from("chat_messages")
			.select("*")
			.eq("conversation_id", conversation.id)
			.order("created_at", { ascending: true });

		if (getError) {
			console.error("❌ Error getting messages:", getError);
		} else {
			console.log("✅ Retrieved messages:", messages.length);
			messages.forEach((msg, index) => {
				console.log(`  ${index + 1}. ${msg.role}: ${msg.content}`);
			});
		}

		// 6. Clean up test data
		console.log("6. Cleaning up test data...");
		await supabase
			.from("chat_conversations")
			.delete()
			.eq("id", conversation.id);
		console.log("✅ Test data cleaned up");

		console.log("\n🎉 Chat creation test completed successfully!");
		console.log("The database schema is working correctly with the current fixes!");
	} catch (error) {
		console.error("Test failed:", error);
	}
}

testWithRealUser();
