const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseSchema() {
	try {
		console.log("Checking current database schema...");

		// Check chat_conversations table structure
		console.log("\n1. Checking chat_conversations table...");
		const { data: convData, error: convError } = await supabase
			.from("chat_conversations")
			.select("*")
			.limit(1);

		if (convError) {
			console.error("❌ chat_conversations error:", convError);
		} else {
			console.log("✅ chat_conversations table exists");
			if (convData && convData.length > 0) {
				console.log("Current columns:", Object.keys(convData[0]));
			} else {
				console.log("Table is empty, cannot determine columns");
			}
		}

		// Check chat_messages table structure
		console.log("\n2. Checking chat_messages table...");
		const { data: msgData, error: msgError } = await supabase
			.from("chat_messages")
			.select("*")
			.limit(1);

		if (msgError) {
			console.error("❌ chat_messages error:", msgError);
		} else {
			console.log("✅ chat_messages table exists");
			if (msgData && msgData.length > 0) {
				console.log("Current columns:", Object.keys(msgData[0]));
			} else {
				console.log("Table is empty, cannot determine columns");
			}
		}

		// Check what tables exist
		console.log("\n3. Checking all tables...");
		const { data: tables, error: tablesError } = await supabase
			.from("information_schema.tables")
			.select("table_name")
			.eq("table_schema", "public");

		if (tablesError) {
			console.error("❌ Error getting tables:", tablesError);
		} else {
			console.log("Available tables:", tables.map(t => t.table_name));
		}

		console.log("\nSchema check completed!");
	} catch (error) {
		console.error("Schema check failed:", error);
	}
}

checkDatabaseSchema();
