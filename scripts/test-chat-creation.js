const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testChatCreation() {
	try {
		console.log("Testing chat creation with current schema...");

		const testUserId = "00000000-0000-0000-0000-000000000000"; // Test UUID

		// 1. Test conversation creation
		console.log("1. Creating conversation...");
		const { data: conversation, error: convError } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: testUserId,
				title: "Test Conversation",
			})
			.select()
			.single();

		if (convError) {
			console.error("❌ Error creating conversation:", convError);
			return;
		}
		console.log("✅ Conversation created:", conversation.id);

		// 2. Test message creation
		console.log("2. Creating message...");
		const { data: message, error: msgError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: "user", // Using 'role' column as it exists in DB
				content: "Hello, this is a test message",
				metadata: { test: true },
			})
			.select()
			.single();

		if (msgError) {
			console.error("❌ Error creating message:", msgError);
		} else {
			console.log("✅ Message created:", message.id);
		}

		// 3. Test AI response message
		console.log("3. Creating AI response...");
		const { data: aiMessage, error: aiError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: conversation.id,
				role: "ai",
				content: "Hello! This is a test AI response.",
				metadata: { model: "gpt-4o-mini" },
			})
			.select()
			.single();

		if (aiError) {
			console.error("❌ Error creating AI message:", aiError);
		} else {
			console.log("✅ AI message created:", aiMessage.id);
		}

		// 4. Test retrieving conversation messages
		console.log("4. Retrieving conversation messages...");
		const { data: messages, error: getError } = await supabase
			.from("chat_messages")
			.select("*")
			.eq("conversation_id", conversation.id)
			.order("created_at", { ascending: true });

		if (getError) {
			console.error("❌ Error getting messages:", getError);
		} else {
			console.log("✅ Retrieved messages:", messages.length);
			messages.forEach((msg, index) => {
				console.log(`  ${index + 1}. ${msg.role}: ${msg.content}`);
			});
		}

		// 5. Clean up test data
		console.log("5. Cleaning up test data...");
		await supabase
			.from("chat_conversations")
			.delete()
			.eq("id", conversation.id);
		console.log("✅ Test data cleaned up");

		console.log("\n🎉 Chat creation test completed successfully!");
	} catch (error) {
		console.error("Test failed:", error);
	}
}

testChatCreation();
