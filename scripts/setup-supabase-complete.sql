-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create the trade-images bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'trade-images',
  'trade-images', 
  true,
  52428800, -- 50MB limit
  ARRAY['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp']
)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own files" ON storage.objects;

-- Create RLS policies for the trade-images bucket
CREATE POLICY "Public can view trade images" ON storage.objects 
FOR SELECT USING (bucket_id = 'trade-images');

CREATE POLICY "Authenticated users can upload trade images" ON storage.objects 
FOR INSERT WITH CHECK (
  bucket_id = 'trade-images' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Users can update their own trade images" ON storage.objects 
FOR UPDATE USING (
  bucket_id = 'trade-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own trade images" ON storage.objects 
FOR DELETE USING (
  bucket_id = 'trade-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);
