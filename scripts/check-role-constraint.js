const { createClient } = require("@supabase/supabase-js");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkRoleConstraint() {
	try {
		console.log("Checking role constraint and existing data...");

		// 1. Check existing messages to see what role values are used
		console.log("1. Checking existing role values...");
		const { data: messages, error: msgError } = await supabase
			.from("chat_messages")
			.select("role")
			.limit(10);

		if (msgError) {
			console.error("❌ Error getting messages:", msgError);
		} else {
			const uniqueRoles = [...new Set(messages.map(m => m.role))];
			console.log("✅ Existing role values:", uniqueRoles);
		}

		// 2. Test different role values to see which ones work
		const testUserId = "a1d3aa48-1033-428d-99c2-f1c3b25df801"; // Real user ID from previous test
		
		// Create a test conversation
		const { data: conversation, error: convError } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: testUserId,
				title: "Role Test Conversation",
			})
			.select()
			.single();

		if (convError) {
			console.error("❌ Error creating test conversation:", convError);
			return;
		}

		console.log("2. Testing different role values...");
		const rolesToTest = ["user", "ai", "assistant", "system", "bot"];

		for (const role of rolesToTest) {
			try {
				const { data: message, error: testError } = await supabase
					.from("chat_messages")
					.insert({
						conversation_id: conversation.id,
						role: role,
						content: `Test message with role: ${role}`,
						metadata: { test: true },
					})
					.select()
					.single();

				if (testError) {
					console.log(`❌ Role '${role}' failed:`, testError.message);
				} else {
					console.log(`✅ Role '${role}' works! Message ID:`, message.id);
				}
			} catch (err) {
				console.log(`❌ Role '${role}' failed:`, err.message);
			}
		}

		// Clean up test conversation
		await supabase
			.from("chat_conversations")
			.delete()
			.eq("id", conversation.id);

		console.log("✅ Test conversation cleaned up");
	} catch (error) {
		console.error("Test failed:", error);
	}
}

checkRoleConstraint();
