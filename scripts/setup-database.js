const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Hardcode the environment variables for now
const supabaseUrl = "https://znkjypzbvlykdmstyeux.supabase.co";
const supabaseServiceKey =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpua2p5cHpidmx5a2Rtc3R5ZXV4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDk0MDc3NCwiZXhwIjoyMDY2NTE2Nzc0fQ.1RcGbo2aJLjbxuoFhUprzonBSIpYskSQAFgebC770aY";

if (!supabaseUrl || !supabaseServiceKey) {
	console.error("Missing Supabase configuration");
	process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
	try {
		console.log("Setting up chat database schema...");

		// Read the SQL file
		const sqlPath = path.join(__dirname, "setup-chat-schema.sql");
		const sql = fs.readFileSync(sqlPath, "utf8");

		// Split SQL into individual statements
		const statements = sql
			.split(";")
			.map((stmt) => stmt.trim())
			.filter((stmt) => stmt.length > 0);

		console.log(`Executing ${statements.length} SQL statements...`);

		for (let i = 0; i < statements.length; i++) {
			const statement = statements[i];
			if (statement.trim()) {
				console.log(`Executing statement ${i + 1}/${statements.length}`);
				try {
					const { error } = await supabase.rpc("exec_sql", {
						sql_query: statement,
					});
					if (error) {
						console.error(`Error in statement ${i + 1}:`, error);
					}
				} catch (err) {
					console.error(`Error executing statement ${i + 1}:`, err.message);
				}
			}
		}

		console.log("Database setup completed!");

		// Test the tables
		console.log("Testing table creation...");
		const { data, error } = await supabase
			.from("chat_conversations")
			.select("*")
			.limit(1);

		if (error) {
			console.error("Error testing chat_conversations table:", error);
		} else {
			console.log("✅ chat_conversations table is accessible");
		}
	} catch (error) {
		console.error("Setup failed:", error);
		process.exit(1);
	}
}

setupDatabase();
