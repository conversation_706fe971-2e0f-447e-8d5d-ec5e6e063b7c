"use server";

import { createServerClient } from "../../lib/supabase";

export async function uploadImageToSupabase(
	formData: FormData,
	userId: string
) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const file = formData.get("file") as File;
		if (!file) {
			return { success: false, error: "No file provided" };
		}

		const fileExt = file.name.split(".").pop();
		const fileName = `${userId}/${Date.now()}-${Math.random()
			.toString(36)
			.substr(2, 9)}.${fileExt}`;

		// Upload using server client (bypasses RLS)
		const { error: uploadError } = await supabase.storage
			.from("trade-images")
			.upload(fileName, file, {
				cacheControl: "3600",
				upsert: false,
			});

		if (uploadError) {
			console.error("Upload error:", uploadError);
			return { success: false, error: uploadError.message };
		}

		// Get public URL
		const { data: urlData } = supabase.storage
			.from("trade-images")
			.getPublicUrl(fileName);

		return { success: true, url: urlData.publicUrl };
	} catch (error) {
		console.error("Error in uploadImageToSupabase:", error);
		return { success: false, error: "Failed to upload image" };
	}
}
