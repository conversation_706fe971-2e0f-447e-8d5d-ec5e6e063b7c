"use server";

import { createServerClient } from "../../lib/supabase";
import { revalidatePath } from "next/cache";
import { randomBytes } from "crypto";

export interface TradeData {
	symbol: string;
	trade_date: string;
	image_url?: string;
	ai_analysis?: string | Record<string, unknown>;
}

export interface PaginationParams {
	page: number;
	limit: number;
}

// Generate a secure random token using base64 and making it URL-safe
function generateShareToken(): string {
	return randomBytes(16)
		.toString("base64")
		.replace(/\+/g, "-")
		.replace(/\//g, "_")
		.replace(/=/g, "");
}

export async function createTrade(userId: string, tradeData: TradeData) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const { data, error } = await supabase
			.from("trades")
			.insert({
				user_id: userId,
				symbol: tradeData.symbol.toUpperCase(),
				trade_date: tradeData.trade_date,
				image_url: tradeData.image_url,
				ai_analysis: tradeData.ai_analysis,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating trade:", error);
			return { success: false, error: error.message };
		}

		revalidatePath("/");
		return { success: true, data };
	} catch (error) {
		console.error("Error in createTrade:", error);
		return { success: false, error: "Failed to create trade" };
	}
}

export async function updateTrade(
	tradeId: string,
	userId: string,
	tradeData: Partial<TradeData>
) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const updateData: Partial<{
			symbol: string;
			trade_date: string;
			image_url: string | null;
			ai_analysis: string | Record<string, unknown> | null;
		}> = {};

		if (tradeData.symbol) updateData.symbol = tradeData.symbol.toUpperCase();
		if (tradeData.trade_date) updateData.trade_date = tradeData.trade_date;
		if (tradeData.image_url !== undefined)
			updateData.image_url = tradeData.image_url;
		if (tradeData.ai_analysis !== undefined)
			updateData.ai_analysis = tradeData.ai_analysis;

		const { data, error } = await supabase
			.from("trades")
			.update(updateData)
			.eq("id", tradeId)
			.eq("user_id", userId)
			.select()
			.single();

		if (error) {
			console.error("Error updating trade:", error);
			return { success: false, error: error.message };
		}

		revalidatePath("/");
		return { success: true, data };
	} catch (error) {
		console.error("Error in updateTrade:", error);
		return { success: false, error: "Failed to update trade" };
	}
}

export async function deleteTrade(tradeId: string, userId: string) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const { error } = await supabase
			.from("trades")
			.delete()
			.eq("id", tradeId)
			.eq("user_id", userId);

		if (error) {
			console.error("Error deleting trade:", error);
			return { success: false, error: error.message };
		}

		revalidatePath("/");
		return { success: true };
	} catch (error) {
		console.error("Error in deleteTrade:", error);
		return { success: false, error: "Failed to delete trade" };
	}
}

export async function shareTrade(tradeId: string, userId: string) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		// Generate share token using compatible base64 encoding
		const shareToken = generateShareToken();

		const { data, error } = await supabase
			.from("trades")
			.update({
				is_shared: true,
				share_token: shareToken,
				shared_at: new Date().toISOString(),
			})
			.eq("id", tradeId)
			.eq("user_id", userId)
			.select()
			.single();

		if (error) {
			console.error("Error sharing trade:", error);
			return { success: false, error: error.message };
		}

		const baseUrl =
			process.env.NEXT_PUBLIC_APP_URL ||
			(typeof window !== "undefined"
				? window.location.origin
				: "http://localhost:3000");
		const shareUrl = `${baseUrl}/share/${shareToken}`;

		revalidatePath("/");
		return { success: true, data, shareUrl };
	} catch (error) {
		console.error("Error in shareTrade:", error);
		return { success: false, error: "Failed to share trade" };
	}
}

export async function unshareTrade(tradeId: string, userId: string) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const { data, error } = await supabase
			.from("trades")
			.update({
				is_shared: false,
				share_token: null,
				shared_at: null,
			})
			.eq("id", tradeId)
			.eq("user_id", userId)
			.select()
			.single();

		if (error) {
			console.error("Error unsharing trade:", error);
			return { success: false, error: error.message };
		}

		revalidatePath("/");
		return { success: true, data };
	} catch (error) {
		console.error("Error in unshareTrade:", error);
		return { success: false, error: "Failed to unshare trade" };
	}
}

export async function getSharedTrade(shareToken: string) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const { data, error } = await supabase
			.from("trades")
			.select("*")
			.eq("share_token", shareToken)
			.eq("is_shared", true)
			.single();

		if (error) {
			console.error("Error fetching shared trade:", error);
			return { success: false, error: "Trade not found or not shared" };
		}

		return { success: true, data };
	} catch (error) {
		console.error("Error in getSharedTrade:", error);
		return { success: false, error: "Failed to fetch shared trade" };
	}
}

export async function getTrades(userId: string, pagination?: PaginationParams) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const page = pagination?.page || 1;
		const limit = pagination?.limit || 10;
		const offset = (page - 1) * limit;

		// Get total count
		const { count, error: countError } = await supabase
			.from("trades")
			.select("*", { count: "exact", head: true })
			.eq("user_id", userId);

		if (countError) {
			console.error("Error counting trades:", countError);
			return { success: false, error: countError.message };
		}

		// Get paginated data
		const { data, error } = await supabase
			.from("trades")
			.select("*")
			.eq("user_id", userId)
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		if (error) {
			console.error("Error fetching trades:", error);
			return { success: false, error: error.message };
		}

		const totalPages = Math.ceil((count || 0) / limit);

		return {
			success: true,
			data,
			pagination: {
				page,
				limit,
				total: count || 0,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
		};
	} catch (error) {
		console.error("Error in getTrades:", error);
		return { success: false, error: "Failed to fetch trades" };
	}
}

export async function updateTradeAnalysis(
	tradeId: string,
	userId: string,
	analysis: string | Record<string, unknown>
) {
	const supabase = createServerClient();

	if (!supabase) {
		return { success: false, error: "Supabase not configured" };
	}

	try {
		const { data, error } = await supabase
			.from("trades")
			.update({ ai_analysis: analysis })
			.eq("id", tradeId)
			.eq("user_id", userId)
			.select()
			.single();

		if (error) {
			console.error("Error updating trade analysis:", error);
			return { success: false, error: error.message };
		}

		revalidatePath("/");
		return { success: true, data };
	} catch (error) {
		console.error("Error in updateTradeAnalysis:", error);
		return { success: false, error: "Failed to update trade analysis" };
	}
}
