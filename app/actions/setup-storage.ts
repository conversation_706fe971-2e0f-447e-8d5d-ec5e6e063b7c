"use server"

import { createServerClient } from "../../lib/supabase"

export async function setupStorageBucket() {
  const supabase = createServerClient()

  if (!supabase) {
    return { success: false, error: "Supabase not configured" }
  }

  try {
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()

    if (listError) {
      console.error("Error listing buckets:", listError)
    }

    const bucketExists = buckets?.some((bucket) => bucket.name === "trade-images")

    if (!bucketExists) {
      // Create bucket with service role
      const { error: createError } = await supabase.storage.createBucket("trade-images", {
        public: true,
        allowedMimeTypes: ["image/png", "image/jpeg", "image/jpg", "image/gif", "image/webp"],
        fileSizeLimit: 52428800, // 50MB
      })

      if (createError) {
        console.error("Error creating bucket:", createError)
        return { success: false, error: `Failed to create bucket: ${createError.message}` }
      }
    }

    return { success: true, message: "Storage bucket ready" }
  } catch (error) {
    console.error("Setup error:", error)
    return { success: false, error: "Failed to setup storage" }
  }
}
