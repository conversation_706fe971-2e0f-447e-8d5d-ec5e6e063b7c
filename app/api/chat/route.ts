import { NextRequest, NextResponse } from "next/server";
import OpenA<PERSON> from "openai";
import { SecurityValidator, ContentFilter } from "../../../lib/utils/security";

// Server-side Azure OpenAI configuration
function getAzureOpenAIClient() {
	const apiKey = process.env.AZURE_OPENAI_API_KEY;
	const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
	const apiVersion = process.env.AZURE_OPENAI_API_VERSION || "2024-10-21";
	const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME || "gpt-4o";

	if (!apiKey) {
		throw new Error("AZURE_OPENAI_API_KEY environment variable is required");
	}
	if (!endpoint) {
		throw new Error("AZURE_OPENAI_ENDPOINT environment variable is required");
	}

	return new OpenAI({
		apiKey,
		baseURL: `${endpoint}/openai/deployments/${deploymentName}`,
		defaultQuery: { "api-version": apiVersion },
		defaultHeaders: {
			"api-key": apiKey,
		},
	});
}

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const {
			messages,
			stream = false,
			max_tokens = 1000,
			temperature = 1,
		} = body;

		// Validate input
		if (!messages || !Array.isArray(messages)) {
			return NextResponse.json(
				{ error: "Messages array is required" },
				{ status: 400 }
			);
		}

		// Validate each message
		for (const message of messages) {
			if (typeof message.content === "string") {
				const validation = SecurityValidator.validateMessage(message.content);
				if (!validation.isValid) {
					return NextResponse.json(
						{
							error: `Invalid message content: ${validation.errors.join(", ")}`,
						},
						{ status: 400 }
					);
				}
			}
		}

		const client = getAzureOpenAIClient();

		if (stream) {
			// Handle streaming response
			const stream = await client.chat.completions.create({
				messages: messages as any,
				model: "o4-mini",
				stream: true,
			});

			// Create a readable stream for the response
			const encoder = new TextEncoder();
			const readable = new ReadableStream({
				async start(controller) {
					try {
						for await (const chunk of stream) {
							const content = chunk.choices[0]?.delta?.content;
							if (content) {
								const filteredContent = ContentFilter.filterResponse(content);
								controller.enqueue(
									encoder.encode(
										`data: ${JSON.stringify({ content: filteredContent })}\n\n`
									)
								);
							}
						}
						controller.enqueue(encoder.encode("data: [DONE]\n\n"));
						controller.close();
					} catch (error) {
						console.error("Streaming error:", error);
						controller.error(error);
					}
				},
			});

			return new Response(readable, {
				headers: {
					"Content-Type": "text/event-stream",
					"Cache-Control": "no-cache",
					Connection: "keep-alive",
				},
			});
		} else {
			// Handle regular response
			const response = await client.chat.completions.create({
				messages: messages as any,
				model: "o4-mini",
				
			});

			const content = response.choices[0]?.message?.content;
			if (!content) {
				return NextResponse.json(
					{ error: "No content received from Azure OpenAI" },
					{ status: 500 }
				);
			}

			// Filter response content
			const filteredContent = ContentFilter.filterResponse(content);

			return NextResponse.json({
				content: filteredContent,
				usage: response.usage,
			});
		}
	} catch (error) {
		console.error("Azure OpenAI API error:", error);

		let errorMessage = "Internal server error";
		let statusCode = 500;

		if (error instanceof Error) {
			if (error.message.includes("API key")) {
				errorMessage = "Invalid API key or authentication failed";
				statusCode = 401;
			} else if (error.message.includes("Rate limit")) {
				errorMessage = "Rate limit exceeded. Please try again later.";
				statusCode = 429;
			} else if (error.message.includes("environment variable")) {
				errorMessage = "Azure OpenAI service is not properly configured";
				statusCode = 503;
			} else {
				errorMessage = error.message;
			}
		}

		return NextResponse.json({ error: errorMessage }, { status: statusCode });
	}
}
