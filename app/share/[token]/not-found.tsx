import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AlertCircle, Home } from "lucide-react"
import Link from "next/link"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-8 text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Trade Not Found</h1>
          <p className="text-gray-600 mb-6">This trade link is invalid, expired, or the trade is no longer shared.</p>
          <Link href="/">
            <Button className="gap-2">
              <Home className="w-4 h-4" />
              Go to TradingVue
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  )
}
