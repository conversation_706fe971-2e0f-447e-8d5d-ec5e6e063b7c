import { getSharedTrade } from "../../actions/trades"
import { SharedTradeView } from "../../../components/shared-trade-view"
import { notFound } from "next/navigation"

interface PageProps {
  params: {
    token: string
  }
}

export default async function SharedTradePage({ params }: PageProps) {
  try {
    const result = await getSharedTrade(params.token)

    if (!result.success || !result.data) {
      notFound()
    }

    return <SharedTradeView trade={result.data} />
  } catch (error) {
    console.error("Error loading shared trade:", error)
    notFound()
  }
}

export async function generateMetadata({ params }: PageProps) {
  try {
    const result = await getSharedTrade(params.token)

    if (result.success && result.data) {
      const trade = result.data
      return {
        title: `${trade.symbol} Trade Analysis - TradingVue`,
        description: `View ${trade.symbol} trade analysis shared on TradingVue. AI-powered insights and recommendations.`,
        openGraph: {
          title: `${trade.symbol} Trade Analysis`,
          description: `AI analysis of ${trade.symbol} trade with ${trade.ai_analysis?.sentiment || "neutral"} sentiment`,
          images: trade.image_url ? [trade.image_url] : [],
        },
      }
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
  }

  return {
    title: "Shared Trade - TradingVue",
    description: "View shared trade analysis on TradingVue",
  }
}
