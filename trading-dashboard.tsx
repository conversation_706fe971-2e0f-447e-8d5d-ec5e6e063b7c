"use client";

import { useState, useEffect } from "react";
import type { User } from "@supabase/supabase-js";
import { Auth } from "./components/auth";
import { supabase, isSupabaseConfigured } from "./lib/supabase";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
	Search,
	CalendarIcon,
	BarChart3,
	TrendingUp,
	BookOpen,
	Notebook as NotebookIcon,
	Plus,
	Users,
	ChevronLeft,
	ChevronRight,
	Filter,
	X,
	MoreHorizontal,
	Copy,
	LogOut,
	UserIcon,
	Bot,
} from "lucide-react";

import { Calendar } from "./components/calendar";
import { Trades } from "./components/trades";
import { ChatInterface } from "./components/chat-interface";
import { Notebook } from "./components/notebook";

export default function Component() {
	const [user, setUser] = useState<User | null>(null);
	const [loading, setLoading] = useState(true);
	const [selectedTab, setSelectedTab] = useState("Overview");
	const [selectedNav, setSelectedNav] = useState("Reports");
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

	useEffect(() => {
		if (!isSupabaseConfigured || !supabase) {
			setLoading(false);
			return;
		}

		// Get initial session
		supabase.auth.getSession().then(({ data: { session } }) => {
			setUser(session?.user ?? null);
			setLoading(false);
		});

		// Listen for auth changes
		const {
			data: { subscription },
		} = supabase.auth.onAuthStateChange((_event, session) => {
			setUser(session?.user ?? null);
		});

		return () => subscription.unsubscribe();
	}, []);

	const handleSignOut = async () => {
		if (supabase) {
			await supabase.auth.signOut();
		}
		setUser(null);
		setSelectedNav("Reports"); // Reset to default view
	};

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-teal-600"></div>
			</div>
		);
	}

	if (!user) {
		return <Auth onAuthChange={setUser} />;
	}

	const tabs = [
		"Overview",
		"Detailed",
		"Win vs Loss",
		"Drawdown",
		"Compare",
		"Tag Breakdown",
		"Advanced",
	];

	const calendarData = [
		{
			date: "06",
			day: "Mon",
			pnl: "$0",
			trades: "0 trades",
			color: "text-gray-500",
		},
		{
			date: "07",
			day: "Tue",
			pnl: "+$21.49k",
			trades: "15 trades",
			color: "text-green-600",
		},
		{
			date: "08",
			day: "Wed",
			pnl: "$0",
			trades: "0 trades",
			color: "text-gray-500",
		},
		{
			date: "09",
			day: "Thu",
			pnl: "-$12.01k",
			trades: "7 trades",
			color: "text-red-600",
		},
		{
			date: "10",
			day: "Fri",
			pnl: "+$52.68k",
			trades: "11 trades",
			color: "text-green-600",
		},
		{
			date: "11",
			day: "Sat",
			pnl: "$0",
			trades: "0 trades",
			color: "text-gray-400",
		},
		{
			date: "12",
			day: "Sun",
			pnl: "$0",
			trades: "0 trades",
			color: "text-gray-400",
		},
	];

	const recentTrades = [
		{
			symbol: "ETSY",
			date: "May 6, 2024 09:30",
			chartColor: "stroke-green-500",
			chartPath: "M10,50 Q20,30 30,40 Q40,20 50,25 Q60,15 70,20",
		},
		{
			symbol: "AAPL",
			date: "May 4, 2024 06:04",
			chartColor: "stroke-red-500",
			chartPath: "M10,30 Q20,20 30,35 Q40,45 50,40 Q60,50 70,45",
		},
		{
			symbol: "NVDA",
			date: "May 1, 2024 11:57",
			chartColor: "stroke-green-500",
			chartPath: "M10,40 Q20,35 30,30 Q40,25 50,35 Q60,30 70,25",
		},
	];

	return (
		<div className="flex h-screen bg-gray-50">
			{/* Left Sidebar */}
			<div className="w-64 bg-white border-r border-gray-200 flex flex-col">
				<div className="p-6 border-b border-gray-200">
					<div className="flex items-center gap-2">
						<div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center">
							<TrendingUp className="w-5 h-5 text-white" />
						</div>
						<span className="text-xl font-semibold">
							Trader<span className="text-teal-600">vue</span>
						</span>
					</div>
				</div>

				<div className="flex-1 p-4">
					<div className="relative mb-6">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
						<Input
							placeholder="Search"
							className="pl-10 bg-gray-50 border-gray-200"
						/>
					</div>

					<nav className="space-y-1">
						<Button
							variant="ghost"
							className={`w-full justify-start gap-3 ${
								selectedNav === "Dashboard"
									? "bg-teal-50 text-teal-700"
									: "text-gray-600 hover:text-gray-900"
							}`}
							onClick={() => setSelectedNav("Dashboard")}
						>
							<BarChart3 className="w-5 h-5" />
							Dashboard
						</Button>
						<Button
							variant="ghost"
							className={`w-full justify-start gap-3 ${
								selectedNav === "Calendar"
									? "bg-teal-50 text-teal-700"
									: "text-gray-600 hover:text-gray-900"
							}`}
							onClick={() => setSelectedNav("Calendar")}
						>
							<CalendarIcon className="w-5 h-5" />
							Calendar
						</Button>
						<Button
							variant="ghost"
							className={`w-full justify-start gap-3 ${
								selectedNav === "Reports"
									? "bg-teal-50 text-teal-700"
									: "text-gray-600 hover:text-gray-900"
							}`}
							onClick={() => setSelectedNav("Reports")}
						>
							<BarChart3 className="w-5 h-5" />
							Reports
						</Button>
						<Button
							variant="ghost"
							className={`w-full justify-start gap-3 ${
								selectedNav === "Trades"
									? "bg-teal-50 text-teal-700"
									: "text-gray-600 hover:text-gray-900"
							}`}
							onClick={() => setSelectedNav("Trades")}
						>
							<TrendingUp className="w-5 h-5" />
							Trades
						</Button>
						<Button
							variant="ghost"
							className="w-full justify-start gap-3 text-gray-600 hover:text-gray-900"
						>
							<BookOpen className="w-5 h-5" />
							Journal
						</Button>
						<Button
							variant="ghost"
							className={`w-full justify-start gap-3 ${
								selectedNav === "Notebook"
									? "bg-teal-50 text-teal-700"
									: "text-gray-600 hover:text-gray-900"
							}`}
							onClick={() => setSelectedNav("Notebook")}
						>
							<NotebookIcon className="w-5 h-5" />
							Notebook
						</Button>
						<Button
							variant="ghost"
							className="w-full justify-start gap-3 text-gray-600 hover:text-gray-900"
						>
							<Plus className="w-5 h-5" />
							New trade
						</Button>
						<Button
							variant="ghost"
							className="w-full justify-start gap-3 text-gray-600 hover:text-gray-900"
						>
							<Users className="w-5 h-5" />
							Community
						</Button>
					</nav>
				</div>

				{/* User Section at Bottom */}
				<div className="p-4 border-t border-gray-200">
					<div className="flex items-center gap-3 mb-3">
						<div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
							<UserIcon className="w-4 h-4 text-gray-600" />
						</div>
						<div className="flex-1 min-w-0">
							<p className="text-sm font-medium text-gray-900 truncate">
								{user.email || "Demo User"}
							</p>
							<p className="text-xs text-gray-500">
								{isSupabaseConfigured ? "Authenticated" : "Demo Mode"}
							</p>
						</div>
					</div>
					<Button
						variant="outline"
						onClick={handleSignOut}
						className="w-full gap-2 text-sm"
					>
						<LogOut className="w-4 h-4" />
						Sign Out
					</Button>
				</div>
			</div>

			{/* Main Content */}
			<div className="flex-1 flex flex-col">
				{/* Header */}
				<div className="bg-white border-b border-gray-200 px-4 py-3">
					{/* Show filters only for Reports */}
					{selectedNav === "Reports" && (
						<>
							{/* Filters */}
							<div className="flex items-center gap-4 mb-6">
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">Symbol</span>
									<Badge variant="secondary" className="bg-gray-100">
										AAPL
									</Badge>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">Tags</span>
									<Select defaultValue="3-selected">
										<SelectTrigger className="w-32">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="3-selected">3 selected</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">Side</span>
									<Select defaultValue="long">
										<SelectTrigger className="w-20">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="long">Long</SelectItem>
											<SelectItem value="short">Short</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">Duration</span>
									<Select defaultValue="all">
										<SelectTrigger className="w-16">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="all">All</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-center gap-2 ml-auto">
									<Button variant="outline" className="gap-2">
										<CalendarIcon className="w-4 h-4" />
										Apr 6 - May 6
									</Button>
									<Button variant="outline" className="gap-2">
										<Filter className="w-4 h-4" />
										Advanced
									</Button>
									<Button variant="outline" className="gap-2">
										<X className="w-4 h-4" />
										Clear
									</Button>
								</div>
							</div>

							{/* Tabs */}
							<div className="flex items-center gap-6 border-b border-gray-200">
								{tabs.map((tab) => (
									<button
										key={tab}
										onClick={() => setSelectedTab(tab)}
										className={`pb-3 px-1 text-sm font-medium border-b-2 transition-colors ${
											selectedTab === tab
												? "border-teal-500 text-teal-600"
												: "border-transparent text-gray-500 hover:text-gray-700"
										}`}
									>
										{tab}
									</button>
								))}
							</div>
						</>
					)}
				</div>

				{/* Content */}
				<div className="flex-1 p-6 overflow-auto">
					{selectedNav === "Calendar" && (
						<div className="max-w-md">
							<Calendar
								selectedDate={selectedDate}
								onDateSelect={setSelectedDate}
							/>
						</div>
					)}

					{selectedNav === "Trades" && (
						<div>
							<Trades user={user} />
						</div>
					)}

					{selectedNav === "Reports" && (
						<>
							{/* Filter Controls */}
							<div className="flex items-center gap-4 mb-6">
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">P&L Type</span>
									<Select defaultValue="gross">
										<SelectTrigger className="w-20">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="gross">Gross</SelectItem>
											<SelectItem value="net">Net</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">View mode</span>
									<Select defaultValue="dollar">
										<SelectTrigger className="w-24">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="dollar">$ Value</SelectItem>
											<SelectItem value="percent">% Value</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">Report type</span>
									<Select defaultValue="aggregate">
										<SelectTrigger className="w-32">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="aggregate">Aggregate P&L</SelectItem>
										</SelectContent>
									</Select>
								</div>
							</div>

							{/* Calendar Header */}
							<div className="flex items-center justify-between mb-6">
								<h2 className="text-xl font-semibold">May, 2024</h2>
								<div className="flex items-center gap-2">
									<Button variant="ghost" size="icon">
										<ChevronLeft className="w-4 h-4" />
									</Button>
									<Button variant="outline" size="sm">
										Today
									</Button>
									<Button variant="ghost" size="icon">
										<ChevronRight className="w-4 h-4" />
									</Button>
								</div>
							</div>

							{/* Calendar Grid */}
							<div className="grid grid-cols-7 gap-4 mb-8">
								{calendarData.map((day, index) => (
									<Card
										key={index}
										className="p-4 hover:shadow-md transition-shadow"
									>
										<CardContent className="p-0">
											<div className="flex items-center justify-between mb-2">
												<span className="text-lg font-semibold">
													{day.date}
												</span>
												<span className="text-xs text-gray-500">{day.day}</span>
												<Copy className="w-3 h-3 text-gray-400" />
											</div>
											<div
												className={`text-lg font-semibold ${day.color} mb-1`}
											>
												{day.pnl}
											</div>
											<div className="text-xs text-gray-500">{day.trades}</div>
										</CardContent>
									</Card>
								))}
							</div>

							{/* Recent Shared Trades */}
							<div className="bg-white rounded-lg border border-gray-200">
								<div className="flex items-center justify-between p-4 border-b border-gray-200">
									<h3 className="text-lg font-semibold">
										Your recent shared trades
									</h3>
									<Button variant="ghost" size="icon">
										<MoreHorizontal className="w-4 h-4" />
									</Button>
								</div>
								<div className="grid grid-cols-3 gap-6 p-6">
									{recentTrades.map((trade, index) => (
										<div key={index} className="space-y-3">
											<div className="text-xs text-gray-500">{trade.date}</div>
											<div className="text-lg font-semibold">
												{trade.symbol}
											</div>
											<div className="h-16 relative">
												<svg className="w-full h-full" viewBox="0 0 80 60">
													<path
														d={trade.chartPath}
														fill="none"
														className={`${trade.chartColor} stroke-2`}
														strokeLinecap="round"
														strokeLinejoin="round"
													/>
												</svg>
											</div>
										</div>
									))}
								</div>
								<div className="px-6 pb-4">
									<div className="flex items-center gap-2 text-sm text-gray-500">
										<span>Open trades</span>
										<span className="text-gray-400">30 days</span>
									</div>
								</div>
							</div>
						</>
					)}

					{selectedNav === "Dashboard" && (
						<div className="h-screen">
							<ChatInterface />
						</div>
					)}

					{selectedNav === "Notebook" && (
						<div className="p-6">
							<Notebook user={user} />
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
