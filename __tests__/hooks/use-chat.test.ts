import { renderHook, act, waitFor } from '@testing-library/react'
import { useChat } from '../../hooks/use-chat'

// Mock the services
jest.mock('../../lib/services/azure-openai-service', () => ({
  azureOpenAIService: {
    generateStreamingChatCompletion: jest.fn(),
    analyzeFiles: jest.fn(),
  },
}))

jest.mock('../../lib/services/chat-database-service', () => ({
  chatDatabaseService: {
    createConversation: jest.fn(),
    createMessage: jest.fn(),
    getConversationMessages: jest.fn(),
    deleteConversation: jest.fn(),
    searchMessages: jest.fn(),
  },
}))

jest.mock('../../lib/utils/file-processing', () => ({
  processUploadedFiles: jest.fn(),
}))

jest.mock('../../lib/azure-openai-config', () => ({
  isAzureOpenAIConfigured: true,
}))

import { azureOpenAIService } from '../../lib/services/azure-openai-service'
import { chatDatabaseService } from '../../lib/services/chat-database-service'
import { processUploadedFiles } from '../../lib/utils/file-processing'

const mockAzureOpenAIService = azureOpenAIService as jest.Mocked<typeof azureOpenAIService>
const mockChatDatabaseService = chatDatabaseService as jest.Mocked<typeof chatDatabaseService>
const mockProcessUploadedFiles = processUploadedFiles as jest.MockedFunction<typeof processUploadedFiles>

describe('useChat', () => {
  const mockUserId = 'test-user-id'
  const mockConversation = {
    id: 'conv-1',
    user_id: mockUserId,
    title: 'Test Conversation',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    metadata: {}
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockChatDatabaseService.createConversation.mockResolvedValue(mockConversation)
    mockChatDatabaseService.createMessage.mockResolvedValue({
      id: 'msg-1',
      conversation_id: 'conv-1',
      type: 'user',
      content: 'Test message',
      metadata: {},
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      is_streaming: false,
      analysis: null
    })
  })

  it('should initialize with empty messages', () => {
    const { result } = renderHook(() => useChat())
    
    expect(result.current.messages).toEqual([])
    expect(result.current.isTyping).toBe(false)
  })

  it('should initialize conversation when user ID is provided', async () => {
    const { result } = renderHook(() => useChat(mockUserId))
    
    await waitFor(() => {
      expect(result.current.isInitialized).toBe(true)
    })

    expect(mockChatDatabaseService.createConversation).toHaveBeenCalledWith(
      mockUserId,
      expect.objectContaining({
        title: 'New Chat'
      })
    )
  })

  it('should send messages successfully', async () => {
    mockAzureOpenAIService.generateStreamingChatCompletion.mockImplementation(
      async (request, onChunk) => {
        onChunk('Hello ')
        onChunk('world!')
      }
    )

    const { result } = renderHook(() => useChat(mockUserId))
    
    await waitFor(() => {
      expect(result.current.isInitialized).toBe(true)
    })

    await act(async () => {
      await result.current.sendMessage({
        content: 'Hello',
        files: [],
      })
    })

    await waitFor(() => {
      expect(result.current.messages).toHaveLength(2) // User message + AI response
    })

    expect(result.current.messages[0].type).toBe('user')
    expect(result.current.messages[0].content).toBe('Hello')
    expect(result.current.messages[1].type).toBe('ai')
    expect(result.current.messages[1].content).toBe('Hello world!')
  })

  it('should handle file uploads', async () => {
    const mockFiles = [
      {
        id: '1',
        name: 'test.jpg',
        type: 'image/jpeg',
        size: 1024,
        url: 'https://example.com/test.jpg'
      }
    ]

    mockProcessUploadedFiles.mockResolvedValue([
      {
        ...mockFiles[0],
        storagePath: 'user/conv/test.jpg'
      }
    ])

    mockAzureOpenAIService.generateStreamingChatCompletion.mockImplementation(
      async (request, onChunk) => {
        onChunk('I can see the image.')
      }
    )

    const { result } = renderHook(() => useChat(mockUserId))
    
    await waitFor(() => {
      expect(result.current.isInitialized).toBe(true)
    })

    await act(async () => {
      await result.current.sendMessage({
        content: 'What do you see?',
        files: mockFiles,
      })
    })

    await waitFor(() => {
      expect(result.current.messages).toHaveLength(2)
    })

    expect(result.current.messages[0].files).toEqual(mockFiles)
  })

  it('should analyze files', async () => {
    const mockAnalysis = {
      summary: 'Test analysis',
      keyPoints: ['Point 1', 'Point 2'],
      suggestions: ['Suggestion 1']
    }

    mockAzureOpenAIService.analyzeFiles.mockResolvedValue(mockAnalysis)

    const { result } = renderHook(() => useChat())

    const mockFiles = [
      {
        id: '1',
        name: 'test.pdf',
        type: 'application/pdf',
        size: 1024,
        url: 'https://example.com/test.pdf'
      }
    ]

    let analysis
    await act(async () => {
      analysis = await result.current.analyzeFiles(mockFiles)
    })

    expect(analysis).toEqual(mockAnalysis)
    expect(mockAzureOpenAIService.analyzeFiles).toHaveBeenCalledWith(mockFiles)
  })

  it('should handle analysis errors gracefully', async () => {
    mockAzureOpenAIService.analyzeFiles.mockRejectedValue(new Error('Analysis failed'))

    const { result } = renderHook(() => useChat())

    const mockFiles = [
      {
        id: '1',
        name: 'test.pdf',
        type: 'application/pdf',
        size: 1024,
        url: 'https://example.com/test.pdf'
      }
    ]

    let analysis
    await act(async () => {
      analysis = await result.current.analyzeFiles(mockFiles)
    })

    expect(analysis.summary).toBe('Failed to analyze files due to an error.')
    expect(analysis.keyPoints).toEqual(['Analysis failed'])
  })

  it('should clear history', async () => {
    const { result } = renderHook(() => useChat(mockUserId))
    
    await waitFor(() => {
      expect(result.current.isInitialized).toBe(true)
    })

    // Add a message first
    await act(async () => {
      await result.current.sendMessage({
        content: 'Test message',
        files: [],
      })
    })

    await waitFor(() => {
      expect(result.current.messages.length).toBeGreaterThan(0)
    })

    // Clear history
    await act(async () => {
      await result.current.clearHistory()
    })

    expect(mockChatDatabaseService.deleteConversation).toHaveBeenCalled()
    expect(result.current.messages).toEqual([])
  })

  it('should search messages', async () => {
    const mockSearchResults = [
      {
        message: {
          id: 'msg-1',
          conversation_id: 'conv-1',
          type: 'user' as const,
          content: 'Test search result',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          is_streaming: false,
          analysis: null
        },
        conversation: mockConversation,
        relevanceScore: 0.9
      }
    ]

    mockChatDatabaseService.searchMessages.mockResolvedValue(mockSearchResults)

    const { result } = renderHook(() => useChat(mockUserId))

    await act(async () => {
      await result.current.searchMessages('test query')
    })

    expect(mockChatDatabaseService.searchMessages).toHaveBeenCalledWith(
      mockUserId,
      'test query'
    )
  })

  it('should export chat data', () => {
    const { result } = renderHook(() => useChat())

    // Mock document methods
    const mockCreateElement = jest.fn().mockReturnValue({
      href: '',
      download: '',
      click: jest.fn(),
    })
    const mockAppendChild = jest.fn()
    const mockRemoveChild = jest.fn()

    Object.defineProperty(document, 'createElement', {
      value: mockCreateElement,
    })
    Object.defineProperty(document.body, 'appendChild', {
      value: mockAppendChild,
    })
    Object.defineProperty(document.body, 'removeChild', {
      value: mockRemoveChild,
    })

    act(() => {
      result.current.exportChat()
    })

    expect(mockCreateElement).toHaveBeenCalledWith('a')
    expect(mockAppendChild).toHaveBeenCalled()
    expect(mockRemoveChild).toHaveBeenCalled()
  })

  it('should handle Azure OpenAI not configured', async () => {
    // Mock Azure OpenAI as not configured
    jest.doMock('../../lib/azure-openai-config', () => ({
      isAzureOpenAIConfigured: false,
    }))

    const { result } = renderHook(() => useChat())

    let analysis
    await act(async () => {
      analysis = await result.current.analyzeFiles([])
    })

    expect(analysis.summary).toBe('File analysis requires Azure OpenAI configuration.')
  })
})
