import { SecurityValidator, ContentFilter } from '../../../lib/utils/security'

describe('SecurityValidator', () => {
  describe('validateMessage', () => {
    it('should validate normal messages', () => {
      const result = SecurityValidator.validateMessage('Hello, how are you?')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.sanitizedContent).toBe('Hello, how are you?')
    })

    it('should reject empty messages', () => {
      const result = SecurityValidator.validateMessage('')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Message cannot be empty')
    })

    it('should reject messages that are too long', () => {
      const longMessage = 'a'.repeat(10001)
      const result = SecurityValidator.validateMessage(longMessage)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Message exceeds maximum length of 10000 characters')
    })

    it('should detect malicious script tags', () => {
      const maliciousMessage = 'Hello <script>alert("xss")</script>'
      const result = SecurityValidator.validateMessage(maliciousMessage)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Message contains potentially malicious content')
    })

    it('should detect javascript: URLs', () => {
      const maliciousMessage = 'Click here: javascript:alert("xss")'
      const result = SecurityValidator.validateMessage(maliciousMessage)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Message contains potentially malicious content')
    })

    it('should sanitize content', () => {
      const message = 'Hello <b>world</b> <script>alert("xss")</script>'
      const result = SecurityValidator.validateMessage(message)
      expect(result.sanitizedContent).not.toContain('<script>')
      expect(result.sanitizedContent).toContain('Hello')
    })
  })

  describe('validateFile', () => {
    it('should validate normal image files', () => {
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const result = SecurityValidator.validateFile(file)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject files that are too large', () => {
      const largeFile = new File(['x'.repeat(51 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' })
      const result = SecurityValidator.validateFile(largeFile)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('File size exceeds maximum of 50MB')
    })

    it('should reject disallowed file types', () => {
      const executableFile = new File(['test'], 'malware.exe', { type: 'application/x-executable' })
      const result = SecurityValidator.validateFile(executableFile)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('File type application/x-executable is not allowed')
    })

    it('should reject files with dangerous extensions', () => {
      const executableFile = new File(['test'], 'malware.exe', { type: 'text/plain' })
      const result = SecurityValidator.validateFile(executableFile)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Executable files are not allowed')
    })

    it('should reject files with path traversal in name', () => {
      const maliciousFile = new File(['test'], '../../../etc/passwd', { type: 'text/plain' })
      const result = SecurityValidator.validateFile(maliciousFile)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('File name contains invalid characters')
    })
  })

  describe('checkRateLimit', () => {
    beforeEach(() => {
      // Clear rate limit store before each test
      SecurityValidator.cleanupRateLimitStore()
    })

    it('should allow requests within rate limit', () => {
      const result = SecurityValidator.checkRateLimit('test-user', { windowMs: 60000, maxRequests: 5 })
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(4)
    })

    it('should block requests exceeding rate limit', () => {
      const config = { windowMs: 60000, maxRequests: 2 }
      
      // First request - should be allowed
      let result = SecurityValidator.checkRateLimit('test-user', config)
      expect(result.allowed).toBe(true)
      
      // Second request - should be allowed
      result = SecurityValidator.checkRateLimit('test-user', config)
      expect(result.allowed).toBe(true)
      
      // Third request - should be blocked
      result = SecurityValidator.checkRateLimit('test-user', config)
      expect(result.allowed).toBe(false)
      expect(result.remaining).toBe(0)
    })

    it('should reset rate limit after window expires', () => {
      const config = { windowMs: 100, maxRequests: 1 }
      
      // First request
      let result = SecurityValidator.checkRateLimit('test-user', config)
      expect(result.allowed).toBe(true)
      
      // Second request - should be blocked
      result = SecurityValidator.checkRateLimit('test-user', config)
      expect(result.allowed).toBe(false)
      
      // Wait for window to expire and try again
      return new Promise(resolve => {
        setTimeout(() => {
          result = SecurityValidator.checkRateLimit('test-user', config)
          expect(result.allowed).toBe(true)
          resolve(undefined)
        }, 150)
      })
    })
  })

  describe('validateApiKey', () => {
    it('should validate proper API keys', () => {
      const validKey = 'sk-1234567890abcdef1234567890abcdef12345678'
      expect(SecurityValidator.validateApiKey(validKey)).toBe(true)
    })

    it('should reject short API keys', () => {
      const shortKey = 'short-key'
      expect(SecurityValidator.validateApiKey(shortKey)).toBe(false)
    })

    it('should reject placeholder API keys', () => {
      const placeholderKey = 'your-api-key'
      expect(SecurityValidator.validateApiKey(placeholderKey)).toBe(false)
    })

    it('should reject null or undefined keys', () => {
      expect(SecurityValidator.validateApiKey(null as any)).toBe(false)
      expect(SecurityValidator.validateApiKey(undefined as any)).toBe(false)
    })
  })

  describe('validateEndpoint', () => {
    it('should validate proper Azure OpenAI endpoints', () => {
      const validEndpoint = 'https://my-resource.openai.azure.com/'
      expect(SecurityValidator.validateEndpoint(validEndpoint)).toBe(true)
    })

    it('should reject non-HTTPS endpoints', () => {
      const httpEndpoint = 'http://my-resource.openai.azure.com/'
      expect(SecurityValidator.validateEndpoint(httpEndpoint)).toBe(false)
    })

    it('should reject non-Azure OpenAI domains', () => {
      const invalidEndpoint = 'https://api.openai.com/'
      expect(SecurityValidator.validateEndpoint(invalidEndpoint)).toBe(false)
    })

    it('should reject malformed URLs', () => {
      const malformedEndpoint = 'not-a-url'
      expect(SecurityValidator.validateEndpoint(malformedEndpoint)).toBe(false)
    })
  })

  describe('generateSecureId', () => {
    it('should generate unique IDs', () => {
      const id1 = SecurityValidator.generateSecureId()
      const id2 = SecurityValidator.generateSecureId()
      expect(id1).not.toBe(id2)
      expect(id1.length).toBeGreaterThan(10)
      expect(id2.length).toBeGreaterThan(10)
    })
  })
})

describe('ContentFilter', () => {
  describe('filterResponse', () => {
    it('should filter out sensitive information', () => {
      const content = 'Your password is: secret123 and your token is abc123'
      const filtered = ContentFilter.filterResponse(content)
      expect(filtered).toContain('[REDACTED]')
      expect(filtered).not.toContain('secret123')
    })

    it('should filter credit card numbers', () => {
      const content = 'My credit card is 1234-5678-9012-3456'
      const filtered = ContentFilter.filterResponse(content)
      expect(filtered).toContain('[REDACTED]')
      expect(filtered).not.toContain('1234-5678-9012-3456')
    })

    it('should filter SSN patterns', () => {
      const content = 'My SSN is ***********'
      const filtered = ContentFilter.filterResponse(content)
      expect(filtered).toContain('[REDACTED]')
      expect(filtered).not.toContain('***********')
    })

    it('should leave normal content unchanged', () => {
      const content = 'This is a normal message without sensitive information.'
      const filtered = ContentFilter.filterResponse(content)
      expect(filtered).toBe(content)
    })
  })

  describe('containsSensitiveInfo', () => {
    it('should detect sensitive information', () => {
      const content = 'Your password is: secret123'
      expect(ContentFilter.containsSensitiveInfo(content)).toBe(true)
    })

    it('should return false for normal content', () => {
      const content = 'This is a normal message.'
      expect(ContentFilter.containsSensitiveInfo(content)).toBe(false)
    })
  })
})
