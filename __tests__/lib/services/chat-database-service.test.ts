import { ChatDatabaseService } from '../../../lib/services/chat-database-service'

// Mock Supabase
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  single: jest.fn(),
  textSearch: jest.fn().mockReturnThis(),
  storage: {
    from: jest.fn().mockReturnThis(),
    remove: jest.fn(),
  },
}

jest.mock('../../../lib/supabase', () => ({
  supabase: mockSupabase,
}))

describe('ChatDatabaseService', () => {
  let service: ChatDatabaseService
  const mockUserId = 'test-user-id'
  const mockConversationId = 'test-conversation-id'

  beforeEach(() => {
    service = new ChatDatabaseService()
    jest.clearAllMocks()
  })

  describe('createConversation', () => {
    it('should create a conversation successfully', async () => {
      const mockConversation = {
        id: mockConversationId,
        user_id: mockUserId,
        title: 'Test Conversation',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        metadata: {}
      }

      mockSupabase.single.mockResolvedValue({
        data: mockConversation,
        error: null
      })

      const result = await service.createConversation(mockUserId, {
        title: 'Test Conversation'
      })

      expect(result).toEqual(mockConversation)
      expect(mockSupabase.from).toHaveBeenCalledWith('chat_conversations')
      expect(mockSupabase.insert).toHaveBeenCalledWith({
        user_id: mockUserId,
        title: 'Test Conversation',
        metadata: {}
      })
    })

    it('should handle database errors', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      await expect(service.createConversation(mockUserId, {}))
        .rejects.toThrow('Failed to create conversation: Database error')
    })
  })

  describe('getConversation', () => {
    it('should retrieve a conversation successfully', async () => {
      const mockConversation = {
        id: mockConversationId,
        user_id: mockUserId,
        title: 'Test Conversation',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        metadata: {}
      }

      mockSupabase.single.mockResolvedValue({
        data: mockConversation,
        error: null
      })

      const result = await service.getConversation(mockConversationId)

      expect(result).toEqual(mockConversation)
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', mockConversationId)
    })

    it('should return null when conversation not found', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found error code
      })

      const result = await service.getConversation(mockConversationId)

      expect(result).toBeNull()
    })
  })

  describe('getUserConversations', () => {
    it('should retrieve user conversations', async () => {
      const mockConversations = [
        {
          id: 'conv-1',
          user_id: mockUserId,
          title: 'Conversation 1',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          metadata: {}
        },
        {
          id: 'conv-2',
          user_id: mockUserId,
          title: 'Conversation 2',
          created_at: '2023-01-02T00:00:00Z',
          updated_at: '2023-01-02T00:00:00Z',
          metadata: {}
        }
      ]

      mockSupabase.limit.mockResolvedValue({
        data: mockConversations,
        error: null
      })

      const result = await service.getUserConversations(mockUserId)

      expect(result).toEqual(mockConversations)
      expect(mockSupabase.eq).toHaveBeenCalledWith('user_id', mockUserId)
      expect(mockSupabase.order).toHaveBeenCalledWith('updated_at', { ascending: false })
      expect(mockSupabase.limit).toHaveBeenCalledWith(50)
    })
  })

  describe('createMessage', () => {
    it('should create a message successfully', async () => {
      const mockMessage = {
        id: 'msg-1',
        conversation_id: mockConversationId,
        type: 'user',
        content: 'Test message',
        metadata: {},
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_streaming: false,
        analysis: null
      }

      mockSupabase.single.mockResolvedValue({
        data: mockMessage,
        error: null
      })

      // Mock the file attachments insert
      mockSupabase.insert.mockResolvedValueOnce({
        data: mockMessage,
        error: null
      }).mockResolvedValueOnce({
        data: [],
        error: null
      })

      // Mock the conversation update
      mockSupabase.update.mockResolvedValue({
        data: null,
        error: null
      })

      const result = await service.createMessage({
        conversation_id: mockConversationId,
        type: 'user',
        content: 'Test message',
        metadata: {},
        file_attachments: []
      })

      expect(result).toEqual(mockMessage)
      expect(mockSupabase.from).toHaveBeenCalledWith('chat_messages')
    })

    it('should handle file attachments', async () => {
      const mockMessage = {
        id: 'msg-1',
        conversation_id: mockConversationId,
        type: 'user',
        content: 'Test message',
        metadata: {},
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_streaming: false,
        analysis: null
      }

      const mockAttachments = [
        {
          file_name: 'test.jpg',
          file_type: 'image/jpeg',
          file_size: 1024,
          storage_path: 'path/to/file',
          preview_url: 'https://example.com/preview',
          content_extracted: null,
          metadata: {}
        }
      ]

      mockSupabase.single.mockResolvedValue({
        data: mockMessage,
        error: null
      })

      mockSupabase.insert
        .mockResolvedValueOnce({ data: mockMessage, error: null })
        .mockResolvedValueOnce({ data: [], error: null })

      mockSupabase.update.mockResolvedValue({
        data: null,
        error: null
      })

      await service.createMessage({
        conversation_id: mockConversationId,
        type: 'user',
        content: 'Test message',
        file_attachments: mockAttachments
      })

      expect(mockSupabase.insert).toHaveBeenCalledTimes(2) // Message + attachments
    })
  })

  describe('getConversationMessages', () => {
    it('should retrieve conversation messages', async () => {
      const mockMessages = [
        {
          id: 'msg-1',
          conversation_id: mockConversationId,
          type: 'user',
          content: 'Hello',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          is_streaming: false,
          analysis: null,
          file_attachments: []
        },
        {
          id: 'msg-2',
          conversation_id: mockConversationId,
          type: 'ai',
          content: 'Hi there!',
          metadata: {},
          created_at: '2023-01-01T00:01:00Z',
          updated_at: '2023-01-01T00:01:00Z',
          is_streaming: false,
          analysis: null,
          file_attachments: []
        }
      ]

      mockSupabase.limit.mockResolvedValue({
        data: mockMessages,
        error: null
      })

      const result = await service.getConversationMessages(mockConversationId)

      expect(result).toEqual(mockMessages)
      expect(mockSupabase.eq).toHaveBeenCalledWith('conversation_id', mockConversationId)
      expect(mockSupabase.order).toHaveBeenCalledWith('created_at', { ascending: true })
    })
  })

  describe('searchMessages', () => {
    it('should search messages successfully', async () => {
      const mockSearchResults = [
        {
          id: 'msg-1',
          conversation_id: mockConversationId,
          type: 'user',
          content: 'Search result',
          metadata: {},
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          is_streaming: false,
          analysis: null,
          conversation: {
            id: mockConversationId,
            user_id: mockUserId,
            title: 'Test Conversation',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z',
            metadata: {}
          }
        }
      ]

      mockSupabase.limit.mockResolvedValue({
        data: mockSearchResults,
        error: null
      })

      const result = await service.searchMessages(mockUserId, 'test query')

      expect(result).toHaveLength(1)
      expect(result[0].message.content).toBe('Search result')
      expect(result[0].relevanceScore).toBe(1.0)
      expect(mockSupabase.textSearch).toHaveBeenCalledWith('content', 'test query')
    })
  })

  describe('deleteConversation', () => {
    it('should delete a conversation successfully', async () => {
      mockSupabase.delete.mockResolvedValue({
        data: null,
        error: null
      })

      await service.deleteConversation(mockConversationId)

      expect(mockSupabase.from).toHaveBeenCalledWith('chat_conversations')
      expect(mockSupabase.delete).toHaveBeenCalled()
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', mockConversationId)
    })

    it('should handle deletion errors', async () => {
      mockSupabase.delete.mockResolvedValue({
        data: null,
        error: { message: 'Deletion failed' }
      })

      await expect(service.deleteConversation(mockConversationId))
        .rejects.toThrow('Failed to delete conversation: Deletion failed')
    })
  })
})
