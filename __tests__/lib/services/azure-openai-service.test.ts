import { AzureOpenAIService } from '../../../lib/services/azure-openai-service'
import { AzureOpenAI } from '@azure/openai'

// Mock the Azure OpenAI client
jest.mock('@azure/openai')

describe('AzureOpenAIService', () => {
  let service: AzureOpenAIService
  let mockClient: jest.Mocked<AzureOpenAI>

  beforeEach(() => {
    mockClient = {
      getChatCompletions: jest.fn(),
      streamChatCompletions: jest.fn(),
    } as any

    ;(AzureOpenAI as jest.Mock).mockImplementation(() => mockClient)
    service = new AzureOpenAIService()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('generateChatCompletion', () => {
    it('should generate a chat completion successfully', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Test response' } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      const result = await service.generateChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 100,
        temperature: 0.7
      })

      expect(result).toBe('Test response')
      expect(mockClient.getChatCompletions).toHaveBeenCalledWith(
        'gpt-4o',
        [{ role: 'user', content: 'Hello' }],
        {
          maxTokens: 100,
          temperature: 0.7
        }
      )
    })

    it('should validate input messages', async () => {
      const maliciousMessage = {
        messages: [{ role: 'user', content: '<script>alert("xss")</script>' }]
      }

      await expect(service.generateChatCompletion(maliciousMessage as any))
        .rejects.toThrow('Invalid message content')
    })

    it('should cap max tokens', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Test response' } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      await service.generateChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5000, // Exceeds cap
        temperature: 0.7
      })

      expect(mockClient.getChatCompletions).toHaveBeenCalledWith(
        'gpt-4o',
        [{ role: 'user', content: 'Hello' }],
        {
          maxTokens: 4000, // Should be capped
          temperature: 0.7
        }
      )
    })

    it('should clamp temperature values', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Test response' } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      await service.generateChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 100,
        temperature: 1.5 // Exceeds maximum
      })

      expect(mockClient.getChatCompletions).toHaveBeenCalledWith(
        'gpt-4o',
        [{ role: 'user', content: 'Hello' }],
        {
          maxTokens: 100,
          temperature: 1 // Should be clamped to 1
        }
      )
    })

    it('should handle API errors gracefully', async () => {
      const apiError = new Error('API Error')
      mockClient.getChatCompletions.mockRejectedValue(apiError)

      await expect(service.generateChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }]
      })).rejects.toThrow()
    })

    it('should throw error when no content is received', async () => {
      const mockResponse = {
        choices: [{ message: { content: null } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      await expect(service.generateChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }]
      })).rejects.toThrow('No content received from Azure OpenAI')
    })
  })

  describe('generateStreamingChatCompletion', () => {
    it('should handle streaming responses', async () => {
      const mockEvents = [
        { choices: [{ delta: { content: 'Hello ' } }] },
        { choices: [{ delta: { content: 'world' } }] },
        { choices: [{ delta: { content: '!' } }] }
      ]

      mockClient.streamChatCompletions.mockImplementation(async function* () {
        for (const event of mockEvents) {
          yield event
        }
      } as any)

      const chunks: string[] = []
      const onChunk = (chunk: string) => chunks.push(chunk)

      await service.generateStreamingChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }]
      }, onChunk)

      expect(chunks).toEqual(['Hello ', 'world', '!'])
    })

    it('should handle streaming errors', async () => {
      mockClient.streamChatCompletions.mockRejectedValue(new Error('Streaming error'))

      const onChunk = jest.fn()

      await expect(service.generateStreamingChatCompletion({
        messages: [{ role: 'user', content: 'Hello' }]
      }, onChunk)).rejects.toThrow()
    })
  })

  describe('analyzeImage', () => {
    it('should analyze images successfully', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'This image shows a cat' } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      const result = await service.analyzeImage('https://example.com/image.jpg')

      expect(result).toBe('This image shows a cat')
      expect(mockClient.getChatCompletions).toHaveBeenCalledWith(
        'gpt-4o',
        expect.arrayContaining([
          expect.objectContaining({
            role: 'user',
            content: expect.arrayContaining([
              expect.objectContaining({ type: 'text' }),
              expect.objectContaining({ 
                type: 'image_url',
                image_url: { url: 'https://example.com/image.jpg', detail: 'high' }
              })
            ])
          })
        ]),
        expect.objectContaining({
          maxTokens: 1000,
          temperature: 0.3
        })
      )
    })

    it('should use custom prompt when provided', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Custom analysis' } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      const customPrompt = 'Describe the colors in this image'
      await service.analyzeImage('https://example.com/image.jpg', customPrompt)

      expect(mockClient.getChatCompletions).toHaveBeenCalledWith(
        'gpt-4o',
        expect.arrayContaining([
          expect.objectContaining({
            content: expect.arrayContaining([
              expect.objectContaining({ 
                type: 'text',
                text: customPrompt
              })
            ])
          })
        ]),
        expect.any(Object)
      )
    })
  })

  describe('analyzeFiles', () => {
    it('should analyze files with images', async () => {
      const mockImageAnalysis = 'This image shows a chart'
      const mockResponse = {
        choices: [{ message: { content: mockImageAnalysis } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      const files = [
        {
          id: '1',
          name: 'chart.jpg',
          type: 'image/jpeg',
          size: 1024,
          url: 'https://example.com/chart.jpg'
        }
      ]

      const result = await service.analyzeFiles(files)

      expect(result.imageDescription).toBe(mockImageAnalysis)
      expect(mockClient.getChatCompletions).toHaveBeenCalled()
    })

    it('should analyze files with text content', async () => {
      const mockSummary = 'This document discusses AI technology'
      const mockResponse = {
        choices: [{ message: { content: mockSummary } }]
      }
      mockClient.getChatCompletions.mockResolvedValue(mockResponse as any)

      const files = [
        {
          id: '1',
          name: 'document.txt',
          type: 'text/plain',
          size: 1024,
          url: 'https://example.com/document.txt',
          content: 'This is a document about AI technology and its applications.'
        }
      ]

      const result = await service.analyzeFiles(files)

      expect(result.extractedText).toContain('document.txt')
      expect(result.extractedText).toContain('This is a document about AI technology')
      expect(result.summary).toBe(mockSummary)
    })

    it('should handle analysis errors gracefully', async () => {
      mockClient.getChatCompletions.mockRejectedValue(new Error('Analysis failed'))

      const files = [
        {
          id: '1',
          name: 'test.jpg',
          type: 'image/jpeg',
          size: 1024,
          url: 'https://example.com/test.jpg'
        }
      ]

      await expect(service.analyzeFiles(files)).rejects.toThrow('Failed to analyze files')
    })
  })
})
