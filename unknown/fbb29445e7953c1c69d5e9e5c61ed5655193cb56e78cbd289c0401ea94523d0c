import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";
import { ContentFilter } from "../../../lib/utils/security";

// Server-side Azure OpenAI configuration
function getAzureOpenAIClient() {
	const apiKey = process.env.AZURE_OPENAI_API_KEY;
	const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
	const apiVersion = process.env.AZURE_OPENAI_API_VERSION || "2024-10-21";
	const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME || "gpt-4o";

	if (!apiKey) {
		throw new Error("AZURE_OPENAI_API_KEY environment variable is required");
	}
	if (!endpoint) {
		throw new Error("AZURE_OPENAI_ENDPOINT environment variable is required");
	}

	return new OpenAI({
		apiKey,
		baseURL: `${endpoint}/openai/deployments/${deploymentName}`,
		defaultQuery: { "api-version": apiVersion },
		defaultHeaders: {
			"api-key": apiKey,
		},
	});
}

async function analyzeImage(
	client: OpenAI,
	imageUrl: string,
	prompt?: string
): Promise<string> {
	const messages = [
		{
			role: "user" as const,
			content: [
				{
					type: "text" as const,
					text:
						prompt ||
						"Analyze this image and provide a detailed description of what you see.",
				},
				{
					type: "image_url" as const,
					image_url: {
						url: imageUrl,
						detail: "high" as const,
					},
				},
			],
		},
	];

	const response = await client.chat.completions.create({
		messages: messages as any,
		model: "o4-mini",
	});

	const content = response.choices[0]?.message?.content;
	if (!content) {
		throw new Error("No content received from Azure OpenAI Vision");
	}

	return content;
}

function extractKeyPoints(text: string): string[] {
	const lines = text.split("\n");
	const keyPoints: string[] = [];

	for (const line of lines) {
		const trimmed = line.trim();
		if (trimmed.match(/^[\d\-\*•]\s+/) || trimmed.startsWith("- ")) {
			keyPoints.push(trimmed.replace(/^[\d\-\*•]\s+/, "").trim());
		}
	}

	return keyPoints.slice(0, 5);
}

function extractSuggestions(text: string): string[] {
	const suggestions: string[] = [];
	const lines = text.split("\n");
	let inSuggestionsSection = false;

	for (const line of lines) {
		const trimmed = line.trim().toLowerCase();

		if (trimmed.includes("suggestion") || trimmed.includes("recommend")) {
			inSuggestionsSection = true;
			continue;
		}

		if (
			inSuggestionsSection &&
			(trimmed.match(/^[\d\-\*•]\s+/) || trimmed.startsWith("- "))
		) {
			suggestions.push(
				line
					.trim()
					.replace(/^[\d\-\*•]\s+/, "")
					.trim()
			);
		}
	}

	return suggestions.slice(0, 3);
}

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { files } = body;

		if (!files || !Array.isArray(files)) {
			return NextResponse.json(
				{ error: "Files array is required" },
				{ status: 400 }
			);
		}

		const client = getAzureOpenAIClient();

		const analysis = {
			summary: "",
			keyPoints: [] as string[],
			suggestions: [] as string[],
			imageDescription: "",
			extractedText: "",
		};

		// Analyze images
		const imageFiles = files.filter((f: any) => f.type.startsWith("image/"));
		if (imageFiles.length > 0) {
			const imageDescriptions = await Promise.all(
				imageFiles.map(async (file: any) => {
					try {
						return await analyzeImage(
							client,
							file.url,
							"Describe this image in detail, focusing on any text, charts, diagrams, or important visual elements."
						);
					} catch (error) {
						console.error(`Failed to analyze image ${file.name}:`, error);
						return `Failed to analyze image: ${file.name}`;
					}
				})
			);
			analysis.imageDescription = imageDescriptions.join("\n\n");
		}

		// Analyze text content
		const textFiles = files.filter((f: any) => f.content);
		if (textFiles.length > 0) {
			const combinedText = textFiles
				.map((f: any) => `File: ${f.name}\n${f.content}`)
				.join("\n\n");
			analysis.extractedText = combinedText;

			// Generate summary and insights
			const summaryPrompt = `Analyze the following content and provide:
1. A comprehensive summary
2. Key points and insights
3. Actionable suggestions

Content:
${combinedText}`;

			const summaryResponse = await client.chat.completions.create({
				messages: [
					{
						role: "system",
						content:
							"You are an expert analyst. Provide clear, structured analysis of the given content.",
					},
					{
						role: "user",
						content: summaryPrompt,
					},
				],
				model: "o4-mini",
			});

			const summaryContent = summaryResponse.choices[0]?.message?.content;
			if (summaryContent) {
				analysis.summary = ContentFilter.filterResponse(summaryContent);
				analysis.keyPoints = extractKeyPoints(summaryContent);
				analysis.suggestions = extractSuggestions(summaryContent);
			}
		}

		return NextResponse.json({ analysis });
	} catch (error) {
		console.error("File analysis error:", error);

		let errorMessage = "Failed to analyze files";
		let statusCode = 500;

		if (error instanceof Error) {
			if (error.message.includes("API key")) {
				errorMessage = "Invalid API key or authentication failed";
				statusCode = 401;
			} else if (error.message.includes("Rate limit")) {
				errorMessage = "Rate limit exceeded. Please try again later.";
				statusCode = 429;
			} else if (error.message.includes("environment variable")) {
				errorMessage = "Azure OpenAI service is not properly configured";
				statusCode = 503;
			} else {
				errorMessage = error.message;
			}
		}

		return NextResponse.json({ error: errorMessage }, { status: statusCode });
	}
}
