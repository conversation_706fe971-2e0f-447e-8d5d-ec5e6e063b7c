export interface ChatConversation {
  id: string
  user_id: string
  title: string | null
  created_at: string
  updated_at: string
  metadata: Record<string, any>
}

export interface ChatMessage {
  id: string
  conversation_id: string
  type: 'user' | 'ai' | 'system'
  content: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
  is_streaming: boolean
  analysis: FileAnalysis | null
  file_attachments?: ChatFileAttachment[]
}

export interface ChatFileAttachment {
  id: string
  message_id: string
  file_name: string
  file_type: string
  file_size: number
  storage_path: string
  preview_url: string | null
  content_extracted: string | null
  created_at: string
  metadata: Record<string, any>
}

export interface FileAnalysis {
  summary?: string
  keyPoints?: string[]
  extractedText?: string
  imageDescription?: string
  dataInsights?: string[]
  suggestions?: string[]
}

export interface CreateConversationRequest {
  title?: string
  metadata?: Record<string, any>
}

export interface CreateMessageRequest {
  conversation_id: string
  type: 'user' | 'ai' | 'system'
  content: string
  metadata?: Record<string, any>
  analysis?: FileAnalysis
  file_attachments?: Omit<ChatFileAttachment, 'id' | 'message_id' | 'created_at'>[]
}

export interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  url: string
  preview?: string
  content?: string
}

export interface AzureOpenAIMessage {
  role: 'system' | 'user' | 'assistant'
  content: string | Array<{
    type: 'text' | 'image_url'
    text?: string
    image_url?: {
      url: string
      detail?: 'low' | 'high' | 'auto'
    }
  }>
}

export interface ChatCompletionRequest {
  messages: AzureOpenAIMessage[]
  max_tokens?: number
  temperature?: number
  stream?: boolean
}

export interface ChatSearchResult {
  message: ChatMessage
  conversation: ChatConversation
  relevanceScore: number
}
