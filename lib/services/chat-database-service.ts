import { supabase } from "../supabase";
import type {
	ChatConversation,
	ChatMessage,
	ChatFileAttachment,
	CreateConversationRequest,
	CreateMessageRequest,
	ChatSearchResult,
} from "../types/chat";

export class ChatDatabaseService {
	async createConversation(
		userId: string,
		request: CreateConversationRequest
	): Promise<ChatConversation> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_conversations")
			.insert({
				user_id: userId,
				title: request.title || "New Conversation",
				// Note: metadata column doesn't exist yet, will be added later
			})
			.select()
			.single();

		if (error) {
			throw new Error(`Failed to create conversation: ${error.message}`);
		}

		return data;
	}

	async getConversation(
		conversationId: string
	): Promise<ChatConversation | null> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_conversations")
			.select("*")
			.eq("id", conversationId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return null; // Not found
			}
			throw new Error(`Failed to get conversation: ${error.message}`);
		}

		return data;
	}

	async getUserConversations(
		userId: string,
		limit = 50
	): Promise<ChatConversation[]> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_conversations")
			.select("*")
			.eq("user_id", userId)
			.order("updated_at", { ascending: false })
			.limit(limit);

		if (error) {
			throw new Error(`Failed to get conversations: ${error.message}`);
		}

		return data || [];
	}

	async updateConversation(
		conversationId: string,
		updates: Partial<Pick<ChatConversation, "title" | "metadata">>
	): Promise<ChatConversation> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_conversations")
			.update(updates)
			.eq("id", conversationId)
			.select()
			.single();

		if (error) {
			throw new Error(`Failed to update conversation: ${error.message}`);
		}

		return data;
	}

	async deleteConversation(conversationId: string): Promise<void> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { error } = await supabase
			.from("chat_conversations")
			.delete()
			.eq("id", conversationId);

		if (error) {
			throw new Error(`Failed to delete conversation: ${error.message}`);
		}
	}

	async createMessage(request: CreateMessageRequest): Promise<ChatMessage> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		// Start a transaction
		// Map our 'type' values to database 'role' values
		const roleMapping: Record<string, string> = {
			user: "user",
			ai: "assistant", // Map 'ai' to 'assistant' for database constraint
			system: "system",
		};

		const { data: message, error: messageError } = await supabase
			.from("chat_messages")
			.insert({
				conversation_id: request.conversation_id,
				role: roleMapping[request.type] || "user", // Map type to valid role
				content: request.content,
				metadata: request.metadata || {}, // This column exists
			})
			.select()
			.single();

		if (messageError) {
			throw new Error(`Failed to create message: ${messageError.message}`);
		}

		// Create file attachments if any
		if (request.file_attachments && request.file_attachments.length > 0) {
			const attachments = request.file_attachments.map((attachment) => ({
				...attachment,
				message_id: message.id,
			}));

			const { error: attachmentError } = await supabase
				.from("chat_file_attachments")
				.insert(attachments);

			if (attachmentError) {
				console.error("Failed to create file attachments:", attachmentError);
				// Don't throw here, message was created successfully
			}
		}

		// Update conversation timestamp
		await supabase
			.from("chat_conversations")
			.update({ updated_at: new Date().toISOString() })
			.eq("id", request.conversation_id);

		return message;
	}

	async getConversationMessages(
		conversationId: string,
		limit = 100
	): Promise<ChatMessage[]> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_messages")
			.select(
				`
        *,
        file_attachments:chat_file_attachments(*)
      `
			)
			.eq("conversation_id", conversationId)
			.order("created_at", { ascending: true })
			.limit(limit);

		if (error) {
			throw new Error(`Failed to get messages: ${error.message}`);
		}

		return data || [];
	}

	async updateMessage(
		messageId: string,
		updates: Partial<Pick<ChatMessage, "content">>
	): Promise<ChatMessage> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		// Only update content for now, other columns don't exist yet
		const safeUpdates = { content: updates.content };

		const { data, error } = await supabase
			.from("chat_messages")
			.update(safeUpdates)
			.eq("id", messageId)
			.select()
			.single();

		if (error) {
			throw new Error(`Failed to update message: ${error.message}`);
		}

		return data;
	}

	async deleteMessage(messageId: string): Promise<void> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { error } = await supabase
			.from("chat_messages")
			.delete()
			.eq("id", messageId);

		if (error) {
			throw new Error(`Failed to delete message: ${error.message}`);
		}
	}

	async searchMessages(
		userId: string,
		query: string,
		limit = 20
	): Promise<ChatSearchResult[]> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_messages")
			.select(
				`
        *,
        conversation:chat_conversations!inner(*)
      `
			)
			.eq("conversation.user_id", userId)
			.textSearch("content", query)
			.order("created_at", { ascending: false })
			.limit(limit);

		if (error) {
			throw new Error(`Failed to search messages: ${error.message}`);
		}

		// Map database 'role' values back to our 'type' values
		const typeMapping: Record<string, "user" | "ai" | "system"> = {
			user: "user",
			assistant: "ai", // Map 'assistant' back to 'ai' for our interface
			system: "system",
		};

		return (data || []).map((item) => ({
			message: {
				id: item.id,
				conversation_id: item.conversation_id,
				type: (typeMapping[item.role] || "user") as "user" | "ai" | "system", // Map role back to type
				content: item.content,
				metadata: item.metadata,
				created_at: item.created_at,
				updated_at: item.created_at, // Use created_at since updated_at doesn't exist
				// Note: is_streaming and analysis columns don't exist yet
			},
			conversation: item.conversation,
			relevanceScore: 1.0, // Placeholder - could be enhanced with proper scoring
		}));
	}

	async getFileAttachment(
		attachmentId: string
	): Promise<ChatFileAttachment | null> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		const { data, error } = await supabase
			.from("chat_file_attachments")
			.select("*")
			.eq("id", attachmentId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return null;
			}
			throw new Error(`Failed to get file attachment: ${error.message}`);
		}

		return data;
	}

	async deleteFileAttachment(attachmentId: string): Promise<void> {
		if (!supabase) {
			throw new Error("Supabase not configured");
		}

		// Get attachment info first to delete from storage
		const attachment = await this.getFileAttachment(attachmentId);
		if (attachment) {
			// Delete from storage
			await supabase.storage
				.from("chat-attachments")
				.remove([attachment.storage_path]);
		}

		// Delete from database
		const { error } = await supabase
			.from("chat_file_attachments")
			.delete()
			.eq("id", attachmentId);

		if (error) {
			throw new Error(`Failed to delete file attachment: ${error.message}`);
		}
	}
}

export const chatDatabaseService = new ChatDatabaseService();
