import { NextRequest, NextResponse } from "next/server";
import { SecurityValidator, ContentFilter } from "../utils/security";

export interface SecurityConfig {
	enableRateLimit: boolean;
	enableContentValidation: boolean;
	enableFileValidation: boolean;
	rateLimitConfig?: {
		windowMs: number;
		maxRequests: number;
	};
}

export class SecurityMiddleware {
	private config: SecurityConfig;

	constructor(
		config: SecurityConfig = {
			enableRateLimit: true,
			enableContentValidation: true,
			enableFileValidation: true,
			rateLimitConfig: {
				windowMs: 60000, // 1 minute
				maxRequests: 20,
			},
		}
	) {
		this.config = config;
	}

	async validateRequest(request: NextRequest): Promise<NextResponse | null> {
		try {
			// Get client identifier (IP + User-Agent hash)
			const clientId = this.getClientIdentifier(request);

			// Rate limiting
			if (this.config.enableRateLimit) {
				const rateLimitResult = SecurityValidator.checkRateLimit(
					clientId,
					this.config.rateLimitConfig
				);

				if (!rateLimitResult.allowed) {
					return NextResponse.json(
						{
							error: "Rate limit exceeded",
							resetTime: rateLimitResult.resetTime,
						},
						{
							status: 429,
							headers: {
								"X-RateLimit-Limit":
									this.config.rateLimitConfig?.maxRequests.toString() || "20",
								"X-RateLimit-Remaining": "0",
								"X-RateLimit-Reset":
									rateLimitResult.resetTime?.toString() || "",
							},
						}
					);
				}
			}

			// Content validation for POST requests
			if (request.method === "POST" && this.config.enableContentValidation) {
				const contentType = request.headers.get("content-type");

				if (contentType?.includes("application/json")) {
					const body = await request.json();
					const validationResult = this.validateRequestBody(body);

					if (!validationResult.isValid) {
						return NextResponse.json(
							{
								error: "Invalid request content",
								details: validationResult.errors,
							},
							{ status: 400 }
						);
					}
				}
			}

			return null; // Continue processing
		} catch (error) {
			console.error("Security middleware error:", error);
			return NextResponse.json(
				{ error: "Security validation failed" },
				{ status: 500 }
			);
		}
	}

	private getClientIdentifier(request: NextRequest): string {
		const ip =
			request.ip ||
			request.headers.get("x-forwarded-for") ||
			request.headers.get("x-real-ip") ||
			"unknown";

		const userAgent = request.headers.get("user-agent") || "unknown";

		// Create a simple hash of IP + User-Agent
		return SecurityValidator.hashSensitiveData(ip + userAgent);
	}

	private validateRequestBody(body: Record<string, unknown>): {
		isValid: boolean;
		errors: string[];
	} {
		const errors: string[] = [];

		// Validate message content if present
		if (body.content && typeof body.content === "string") {
			const messageValidation = SecurityValidator.validateMessage(body.content);
			if (!messageValidation.isValid) {
				errors.push(...messageValidation.errors);
			}
		}

		// Validate file data if present
		if (body.files && Array.isArray(body.files)) {
			for (const file of body.files) {
				if (file.size && file.size > 50 * 1024 * 1024) {
					errors.push("File size exceeds limit");
				}
				if (file.type && !this.isAllowedFileType(file.type)) {
					errors.push(`File type ${file.type} not allowed`);
				}
			}
		}

		// Check for suspicious patterns in the entire request
		const requestString = JSON.stringify(body);
		if (this.containsSuspiciousContent(requestString)) {
			errors.push("Request contains suspicious content");
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	private isAllowedFileType(type: string): boolean {
		const allowedTypes = [
			"image/jpeg",
			"image/jpg",
			"image/png",
			"image/gif",
			"image/webp",
			"image/svg+xml",
			"application/pdf",
			"text/plain",
			"text/csv",
			"application/json",
		];
		return allowedTypes.includes(type);
	}

	private containsSuspiciousContent(content: string): boolean {
		const suspiciousPatterns = [
			/<script/gi,
			/javascript:/gi,
			/vbscript:/gi,
			/data:text\/html/gi,
			/eval\s*\(/gi,
			/function\s*\(/gi,
			/setTimeout\s*\(/gi,
			/setInterval\s*\(/gi,
		];

		return suspiciousPatterns.some((pattern) => pattern.test(content));
	}

	filterResponse(content: string): string {
		return ContentFilter.filterResponse(content);
	}
}

// CORS middleware
export function corsMiddleware(request: NextRequest): NextResponse | null {
	// Handle preflight requests
	if (request.method === "OPTIONS") {
		return new NextResponse(null, {
			status: 200,
			headers: {
				"Access-Control-Allow-Origin": process.env.ALLOWED_ORIGINS || "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers":
					"Content-Type, Authorization, X-Requested-With",
				"Access-Control-Max-Age": "86400",
			},
		});
	}

	return null;
}

// Authentication middleware
export function authMiddleware(request: NextRequest): NextResponse | null {
	const authHeader = request.headers.get("authorization");

	// Skip auth for public endpoints
	const publicPaths = ["/api/health", "/api/status"];
	if (publicPaths.some((path) => request.nextUrl.pathname.startsWith(path))) {
		return null;
	}

	if (!authHeader || !authHeader.startsWith("Bearer ")) {
		return NextResponse.json(
			{ error: "Missing or invalid authorization header" },
			{ status: 401 }
		);
	}

	const token = authHeader.substring(7);

	// Validate token format (basic validation)
	if (!token || token.length < 10) {
		return NextResponse.json(
			{ error: "Invalid token format" },
			{ status: 401 }
		);
	}

	return null;
}

// Combined security middleware
export function createSecurityMiddleware(config?: Partial<SecurityConfig>) {
	const middleware = new SecurityMiddleware(config as SecurityConfig);

	return async (request: NextRequest): Promise<NextResponse> => {
		// Apply CORS
		const corsResponse = corsMiddleware(request);
		if (corsResponse) return corsResponse;

		// Apply authentication (optional)
		if (process.env.ENABLE_AUTH === "true") {
			const authResponse = authMiddleware(request);
			if (authResponse) return authResponse;
		}

		// Apply security validation
		const securityResponse = await middleware.validateRequest(request);
		if (securityResponse) return securityResponse;

		// Continue to the actual handler
		return NextResponse.next();
	};
}
