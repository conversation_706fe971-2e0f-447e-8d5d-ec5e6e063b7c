import DOMPurify from 'isomorphic-dompurify'

// Content filtering and validation
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  sanitizedContent?: string
}

export interface RateLimitConfig {
  windowMs: number
  maxRequests: number
}

// Rate limiting store (in-memory for demo, use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export class SecurityValidator {
  private static readonly MAX_MESSAGE_LENGTH = 10000
  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
  private static readonly ALLOWED_FILE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'application/pdf',
    'text/plain',
    'text/csv',
    'application/json',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ]

  // Malicious patterns to detect
  private static readonly MALICIOUS_PATTERNS = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /onload\s*=/gi,
    /onerror\s*=/gi,
    /onclick\s*=/gi,
    /data:text\/html/gi,
    /<iframe\b[^>]*>/gi,
    /<object\b[^>]*>/gi,
    /<embed\b[^>]*>/gi,
    /<link\b[^>]*>/gi,
    /<meta\b[^>]*>/gi
  ]

  static validateMessage(content: string): ValidationResult {
    const errors: string[] = []

    // Check length
    if (content.length > this.MAX_MESSAGE_LENGTH) {
      errors.push(`Message exceeds maximum length of ${this.MAX_MESSAGE_LENGTH} characters`)
    }

    // Check for empty content
    if (!content.trim()) {
      errors.push('Message cannot be empty')
    }

    // Check for malicious patterns
    for (const pattern of this.MALICIOUS_PATTERNS) {
      if (pattern.test(content)) {
        errors.push('Message contains potentially malicious content')
        break
      }
    }

    // Sanitize content
    const sanitizedContent = this.sanitizeContent(content)

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedContent
    }
  }

  static validateFile(file: File): ValidationResult {
    const errors: string[] = []

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      errors.push(`File size exceeds maximum of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`)
    }

    // Check file type
    if (!this.ALLOWED_FILE_TYPES.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`)
    }

    // Check file name for malicious patterns
    const fileName = file.name
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      errors.push('File name contains invalid characters')
    }

    // Check for executable file extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.jar', '.js', '.vbs', '.ps1']
    const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push('Executable files are not allowed')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  static sanitizeContent(content: string): string {
    // Remove potentially dangerous HTML/JS
    let sanitized = content

    // Remove script tags and their content
    sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')

    // Remove dangerous attributes
    sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
    sanitized = sanitized.replace(/\s*javascript\s*:/gi, '')
    sanitized = sanitized.replace(/\s*vbscript\s*:/gi, '')

    // Use DOMPurify for additional sanitization
    if (typeof window !== 'undefined') {
      sanitized = DOMPurify.sanitize(sanitized, {
        ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li', 'blockquote', 'code', 'pre'],
        ALLOWED_ATTR: []
      })
    }

    return sanitized
  }

  static checkRateLimit(
    identifier: string, 
    config: RateLimitConfig = { windowMs: 60000, maxRequests: 10 }
  ): { allowed: boolean; resetTime?: number; remaining?: number } {
    const now = Date.now()
    const key = identifier
    
    const existing = rateLimitStore.get(key)
    
    if (!existing || now > existing.resetTime) {
      // Create new window
      rateLimitStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs
      }
    }
    
    if (existing.count >= config.maxRequests) {
      return {
        allowed: false,
        resetTime: existing.resetTime,
        remaining: 0
      }
    }
    
    existing.count++
    
    return {
      allowed: true,
      remaining: config.maxRequests - existing.count,
      resetTime: existing.resetTime
    }
  }

  static validateApiKey(apiKey: string): boolean {
    if (!apiKey || typeof apiKey !== 'string') {
      return false
    }

    // Basic format validation for Azure OpenAI API keys
    if (apiKey.length < 32) {
      return false
    }

    // Check for common test/placeholder values
    const invalidKeys = [
      'your-api-key',
      'test-key',
      'placeholder',
      'example-key',
      '12345',
      'abc123'
    ]

    return !invalidKeys.includes(apiKey.toLowerCase())
  }

  static validateEndpoint(endpoint: string): boolean {
    if (!endpoint || typeof endpoint !== 'string') {
      return false
    }

    try {
      const url = new URL(endpoint)
      
      // Must be HTTPS
      if (url.protocol !== 'https:') {
        return false
      }

      // Must be Azure OpenAI domain
      if (!url.hostname.includes('openai.azure.com')) {
        return false
      }

      return true
    } catch {
      return false
    }
  }

  static generateSecureId(): string {
    // Generate a cryptographically secure random ID
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      const array = new Uint8Array(16)
      crypto.getRandomValues(array)
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
    }
    
    // Fallback for environments without crypto
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  static hashSensitiveData(data: string): string {
    // Simple hash for client-side (use proper hashing server-side)
    let hash = 0
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }

  static cleanupRateLimitStore(): void {
    const now = Date.now()
    for (const [key, value] of rateLimitStore.entries()) {
      if (now > value.resetTime) {
        rateLimitStore.delete(key)
      }
    }
  }
}

// Content filtering for AI responses
export class ContentFilter {
  private static readonly INAPPROPRIATE_PATTERNS = [
    /\b(password|secret|token|key|credential)\s*[:=]\s*\S+/gi,
    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // Credit card patterns
    /\b\d{3}-\d{2}-\d{4}\b/g, // SSN patterns
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g // Email patterns (optional filtering)
  ]

  static filterResponse(content: string): string {
    let filtered = content

    // Remove potential sensitive information
    for (const pattern of this.INAPPROPRIATE_PATTERNS) {
      filtered = filtered.replace(pattern, '[REDACTED]')
    }

    return filtered
  }

  static containsSensitiveInfo(content: string): boolean {
    return this.INAPPROPRIATE_PATTERNS.some(pattern => pattern.test(content))
  }
}

// Cleanup function to be called periodically
export function cleanupSecurityStore(): void {
  SecurityValidator.cleanupRateLimitStore()
}
