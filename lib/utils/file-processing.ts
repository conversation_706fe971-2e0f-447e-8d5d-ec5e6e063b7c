import { supabase } from '../supabase'
import type { UploadedFile } from '../types/chat'

export interface FileValidationResult {
  isValid: boolean
  error?: string
  fileType?: string
  size?: number
}

export interface ProcessedFile extends UploadedFile {
  base64?: string
  storagePath?: string
}

// Supported file types and their limits
export const FILE_CONSTRAINTS = {
  maxSize: 50 * 1024 * 1024, // 50MB
  allowedImageTypes: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ],
  allowedDocumentTypes: [
    'application/pdf',
    'text/plain',
    'text/csv',
    'application/json',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ]
}

export function validateFile(file: File): FileValidationResult {
  // Check file size
  if (file.size > FILE_CONSTRAINTS.maxSize) {
    return {
      isValid: false,
      error: `File size exceeds ${FILE_CONSTRAINTS.maxSize / (1024 * 1024)}MB limit`
    }
  }

  // Check file type
  const allowedTypes = [
    ...FILE_CONSTRAINTS.allowedImageTypes,
    ...FILE_CONSTRAINTS.allowedDocumentTypes
  ]

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not supported`
    }
  }

  return {
    isValid: true,
    fileType: file.type,
    size: file.size
  }
}

export async function convertFileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      const result = reader.result as string
      resolve(result)
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to convert file to base64'))
    }
    
    reader.readAsDataURL(file)
  })
}

export async function extractTextFromFile(file: File): Promise<string | null> {
  try {
    if (file.type === 'text/plain' || file.type === 'text/csv') {
      return await file.text()
    }
    
    if (file.type === 'application/json') {
      const text = await file.text()
      try {
        const json = JSON.parse(text)
        return JSON.stringify(json, null, 2)
      } catch {
        return text
      }
    }
    
    // For other file types, we'll need server-side processing
    // This is a placeholder for future implementation
    return null
  } catch (error) {
    console.error('Error extracting text from file:', error)
    return null
  }
}

export async function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('File is not an image'))
      return
    }

    const reader = new FileReader()
    
    reader.onload = () => {
      resolve(reader.result as string)
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to create image preview'))
    }
    
    reader.readAsDataURL(file)
  })
}

export async function uploadFileToStorage(
  file: File,
  userId: string,
  conversationId: string
): Promise<{ storagePath: string; publicUrl: string }> {
  if (!supabase) {
    throw new Error('Supabase not configured')
  }

  const fileExtension = file.name.split('.').pop()
  const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExtension}`
  const storagePath = `${userId}/${conversationId}/${fileName}`

  const { data, error } = await supabase.storage
    .from('chat-attachments')
    .upload(storagePath, file, {
      cacheControl: '3600',
      upsert: false
    })

  if (error) {
    throw new Error(`Failed to upload file: ${error.message}`)
  }

  const { data: urlData } = supabase.storage
    .from('chat-attachments')
    .getPublicUrl(storagePath)

  return {
    storagePath: data.path,
    publicUrl: urlData.publicUrl
  }
}

export async function processUploadedFiles(
  files: File[],
  userId: string,
  conversationId: string
): Promise<ProcessedFile[]> {
  const processedFiles: ProcessedFile[] = []

  for (const file of files) {
    try {
      // Validate file
      const validation = validateFile(file)
      if (!validation.isValid) {
        console.error(`File validation failed for ${file.name}: ${validation.error}`)
        continue
      }

      // Create base processed file object
      const processedFile: ProcessedFile = {
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        type: file.type,
        size: file.size,
        url: '', // Will be set after upload
      }

      // Upload to storage
      const { storagePath, publicUrl } = await uploadFileToStorage(file, userId, conversationId)
      processedFile.url = publicUrl
      processedFile.storagePath = storagePath

      // Create preview for images
      if (file.type.startsWith('image/')) {
        try {
          processedFile.preview = await createImagePreview(file)
          processedFile.base64 = await convertFileToBase64(file)
        } catch (error) {
          console.error(`Failed to create preview for ${file.name}:`, error)
        }
      }

      // Extract text content
      const textContent = await extractTextFromFile(file)
      if (textContent) {
        processedFile.content = textContent
      }

      processedFiles.push(processedFile)
    } catch (error) {
      console.error(`Failed to process file ${file.name}:`, error)
    }
  }

  return processedFiles
}

export function getFileIcon(fileType: string): string {
  if (fileType.startsWith('image/')) return '🖼️'
  if (fileType === 'application/pdf') return '📄'
  if (fileType.includes('spreadsheet') || fileType.includes('excel')) return '📊'
  if (fileType.includes('presentation') || fileType.includes('powerpoint')) return '📽️'
  if (fileType.includes('document') || fileType.includes('word')) return '📝'
  if (fileType === 'text/csv') return '📋'
  if (fileType === 'application/json') return '🔧'
  if (fileType.startsWith('text/')) return '📄'
  return '📎'
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function isImageFile(fileType: string): boolean {
  return FILE_CONSTRAINTS.allowedImageTypes.includes(fileType)
}

export function isDocumentFile(fileType: string): boolean {
  return FILE_CONSTRAINTS.allowedDocumentTypes.includes(fileType)
}
