# Stock Trading App with AI Chat Assistant

A comprehensive stock trading application with an integrated AI-powered chat assistant that supports text and image analysis using Azure OpenAI.

## Features

### 🤖 AI Chat Assistant

- **Azure OpenAI Integration**: Powered by GPT-4o for intelligent conversations
- **Image Analysis**: Upload and analyze images with AI vision capabilities
- **Document Processing**: Support for PDFs, text files, and various document formats
- **Streaming Responses**: Real-time AI responses with streaming support
- **Chat History**: Persistent conversation storage with Supabase
- **File Attachments**: Secure file upload and storage

### 🔒 Security & Validation

- **Input Validation**: Comprehensive content filtering and sanitization
- **Rate Limiting**: Built-in protection against abuse
- **File Security**: Secure file handling with type and size validation
- **Content Filtering**: Automatic detection and filtering of sensitive information

### 📊 Trading Features

- **Trade Management**: Create, edit, and track trading positions
- **Notebook Component**: Document trades, strategies, and learning materials
- **Data Visualization**: Charts and analytics for trading performance

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **AI**: Azure OpenAI (GPT-4o with vision)
- **Database**: Supabase (PostgreSQL)
- **Storage**: Supabase Storage for file attachments
- **Authentication**: Supabase Auth
- **Testing**: Jest, React Testing Library
- **Security**: Content filtering, rate limiting, input validation

## Prerequisites

Before you begin, ensure you have:

1. **Node.js** (v18 or higher)
2. **Azure OpenAI** account and API key
3. **Supabase** project set up
4. **Git** for version control

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd stockapp
npm install
```

### 2. Environment Configuration

Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY="your-azure-openai-api-key"
AZURE_OPENAI_ENDPOINT="https://your-resource-name.openai.azure.com/"
AZURE_OPENAI_API_VERSION="2024-02-15-preview"
AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4o"
AZURE_OPENAI_VISION_DEPLOYMENT_NAME="gpt-4o"

# Optional Security Settings
ENABLE_AUTH="true"
ALLOWED_ORIGINS="http://localhost:3000"
```

### 3. Azure OpenAI Setup

1. Create an Azure OpenAI resource in the Azure portal
2. Deploy a GPT-4o model for both text and vision capabilities
3. Copy your API key and endpoint to the environment variables
4. Ensure your deployment names match the environment configuration

### 4. Supabase Setup

#### Database Schema

Run the following SQL scripts in your Supabase SQL editor:

```bash
# Set up chat tables and storage
psql -f scripts/setup-chat-schema.sql
psql -f scripts/setup-chat-storage.sql

# Set up trading tables (if needed)
psql -f scripts/create-trades-table.sql
```

#### Storage Buckets

The chat storage script will create the necessary storage buckets with proper security policies.

### 5. Development

Start the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## Project Structure

```
stockapp/
├── app/                    # Next.js app directory
├── components/             # React components
│   ├── chat-interface.tsx  # Main chat component
│   ├── message-bubble.tsx  # Chat message display
│   ├── file-upload.tsx     # File upload component
│   └── ui/                 # Reusable UI components
├── hooks/                  # Custom React hooks
│   ├── use-chat.ts         # Chat functionality
│   ├── use-auth.ts         # Authentication
│   └── use-theme.ts        # Theme management
├── lib/                    # Utility libraries
│   ├── services/           # Service layer
│   │   ├── azure-openai-service.ts
│   │   └── chat-database-service.ts
│   ├── utils/              # Utility functions
│   │   ├── security.ts     # Security validation
│   │   └── file-processing.ts
│   └── types/              # TypeScript type definitions
├── scripts/                # Database setup scripts
├── __tests__/              # Test files
└── public/                 # Static assets
```
