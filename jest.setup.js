import '@testing-library/jest-dom'

// Mock environment variables
process.env.AZURE_OPENAI_API_KEY = 'test-api-key-12345678901234567890'
process.env.AZURE_OPENAI_ENDPOINT = 'https://test-resource.openai.azure.com/'
process.env.AZURE_OPENAI_API_VERSION = '2024-02-15-preview'
process.env.AZURE_OPENAI_DEPLOYMENT_NAME = 'gpt-4o'
process.env.AZURE_OPENAI_VISION_DEPLOYMENT_NAME = 'gpt-4o'

// Mock Supabase
jest.mock('./lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({ data: { session: null } }),
      onAuthStateChange: jest.fn().mockReturnValue({ data: { subscription: { unsubscribe: jest.fn() } } }),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      resetPasswordForEmail: jest.fn(),
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    storage: {
      from: jest.fn().mockReturnThis(),
      upload: jest.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
      getPublicUrl: jest.fn().mockReturnValue({ data: { publicUrl: 'https://test-url.com' } }),
      remove: jest.fn().mockResolvedValue({ error: null }),
    },
  },
  isSupabaseConfigured: true,
  createServerClient: jest.fn(),
}))

// Mock Azure OpenAI
jest.mock('@azure/openai', () => ({
  AzureOpenAI: jest.fn().mockImplementation(() => ({
    getChatCompletions: jest.fn().mockResolvedValue({
      choices: [{ message: { content: 'Test response' } }]
    }),
    streamChatCompletions: jest.fn().mockImplementation(async function* () {
      yield { choices: [{ delta: { content: 'Test ' } }] }
      yield { choices: [{ delta: { content: 'streaming ' } }] }
      yield { choices: [{ delta: { content: 'response' } }] }
    }),
  })),
}))

// Mock file operations
global.URL.createObjectURL = jest.fn(() => 'mocked-url')
global.URL.revokeObjectURL = jest.fn()

// Mock crypto for secure ID generation
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: jest.fn().mockImplementation((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
  },
})

// Mock navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
  },
})

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))
