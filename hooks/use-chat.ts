"use client";

import { useState, useCallback, useEffect } from "react";
import { chatDatabaseService } from "../lib/services/chat-database-service";
import { processUploadedFiles } from "../lib/utils/file-processing";
import type {
	ChatMessage,
	ChatConversation,
	AzureOpenAIMessage,
	FileAnalysis,
} from "../lib/types/chat";
import type { UploadedFile } from "../components/chat-interface";

interface Message {
	id: string;
	type: "user" | "ai" | "system";
	content: string;
	files?: UploadedFile[];
	timestamp: Date;
	isStreaming?: boolean;
	analysis?: FileAnalysis;
}

export function useChat(userId?: string) {
	const [messages, setMessages] = useState<Message[]>([]);
	const [isTyping, setIsTyping] = useState(false);
	const [currentConversation, setCurrentConversation] =
		useState<ChatConversation | null>(null);
	const [isInitialized, setIsInitialized] = useState(false);

	// Initialize conversation
	useEffect(() => {
		if (userId && !isInitialized) {
			initializeConversation();
		}
	}, [userId, isInitialized]);

	const initializeConversation = useCallback(async () => {
		if (!userId) return;

		try {
			// Create a new conversation
			const conversation = await chatDatabaseService.createConversation(
				userId,
				{
					title: "New Chat",
					metadata: { createdAt: new Date().toISOString() },
				}
			);

			setCurrentConversation(conversation);
			setIsInitialized(true);
		} catch (error) {
			console.error("Failed to initialize conversation:", error);
			// Fall back to local mode
			setIsInitialized(true);
		}
	}, [userId]);

	const loadConversationMessages = useCallback(
		async (conversationId: string) => {
			try {
				const dbMessages = await chatDatabaseService.getConversationMessages(
					conversationId
				);

				const convertedMessages: Message[] = dbMessages.map((msg) => ({
					id: msg.id,
					type: msg.type,
					content: msg.content,
					timestamp: new Date(msg.created_at),
					isStreaming: msg.is_streaming,
					analysis: msg.analysis,
					files: msg.file_attachments?.map((attachment) => ({
						id: attachment.id,
						name: attachment.file_name,
						type: attachment.file_type,
						size: attachment.file_size,
						url: attachment.preview_url || "",
						content: attachment.content_extracted || undefined,
					})),
				}));

				setMessages(convertedMessages);
			} catch (error) {
				console.error("Failed to load conversation messages:", error);
			}
		},
		[]
	);

	const analyzeFiles = useCallback(
		async (files: UploadedFile[]): Promise<FileAnalysis> => {
			try {
				const response = await fetch("/api/analyze-files", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({ files }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || "Failed to analyze files");
				}

				const data = await response.json();
				return data.analysis;
			} catch (error) {
				console.error("File analysis failed:", error);
				return {
					summary: "Failed to analyze files due to an error.",
					keyPoints: ["Analysis failed"],
					suggestions: ["Please try again or check your configuration"],
				};
			}
		},
		[]
	);

	const sendMessage = useCallback(
		async ({
			content,
			files,
			analysis,
		}: {
			content: string;
			files?: UploadedFile[];
			analysis?: FileAnalysis;
		}) => {
			if (!currentConversation && !userId) {
				console.error("No conversation or user ID available");
				return;
			}

			try {
				// Create user message
				const userMessage: Message = {
					id: Math.random().toString(36).substr(2, 9),
					type: "user",
					content,
					files,
					timestamp: new Date(),
				};

				setMessages((prev) => [...prev, userMessage]);

				// Save user message to database if conversation exists
				if (currentConversation && userId) {
					try {
						let processedFiles: any[] = [];
						if (files && files.length > 0) {
							// Convert File objects to processed files
							const fileObjects = files.map((f) => {
								// Create a File object from UploadedFile
								const blob = new Blob([], { type: f.type });
								return new File([blob], f.name, { type: f.type });
							});
							processedFiles = await processUploadedFiles(
								fileObjects,
								userId,
								currentConversation.id
							);
						}

						await chatDatabaseService.createMessage({
							conversation_id: currentConversation.id,
							type: "user",
							content,
							analysis,
							file_attachments: processedFiles.map((f) => ({
								file_name: f.name,
								file_type: f.type,
								file_size: f.size,
								storage_path: f.storagePath || "",
								preview_url: f.url,
								content_extracted: f.content || null,
								metadata: {},
							})),
						});
					} catch (dbError) {
						console.error("Failed to save user message:", dbError);
					}
				}

				// Generate AI response
				setIsTyping(true);
				await generateAIResponse(content, files, analysis);
			} catch (error) {
				console.error("Error sending message:", error);
				setIsTyping(false);
			}
		},
		[currentConversation, userId]
	);

	const generateAIResponse = async (
		content: string,
		files?: UploadedFile[],
		analysis?: FileAnalysis
	): Promise<void> => {
		try {
			// Build conversation context
			const conversationMessages: AzureOpenAIMessage[] = [
				{
					role: "system",
					content:
						"You are a helpful AI assistant specialized in analyzing documents, images, and answering questions. Provide clear, detailed, and actionable responses.",
				},
			];

			// Add recent messages for context (last 10 messages)
			const recentMessages = messages.slice(-10);
			for (const msg of recentMessages) {
				if (msg.type === "user") {
					let messageContent: any = msg.content;

					// Add image content if present
					if (msg.files && msg.files.length > 0) {
						const imageFiles = msg.files.filter((f) =>
							f.type.startsWith("image/")
						);
						if (imageFiles.length > 0) {
							messageContent = [
								{ type: "text", text: msg.content },
								...imageFiles.map((img) => ({
									type: "image_url",
									image_url: { url: img.url, detail: "high" },
								})),
							];
						}
					}

					conversationMessages.push({
						role: "user",
						content: messageContent,
					});
				} else if (msg.type === "ai") {
					conversationMessages.push({
						role: "assistant",
						content: msg.content,
					});
				}
			}

			// Add current message
			let currentMessageContent: any = content;
			if (files && files.length > 0) {
				const imageFiles = files.filter((f) => f.type.startsWith("image/"));
				if (imageFiles.length > 0) {
					currentMessageContent = [
						{ type: "text", text: content },
						...imageFiles.map((img) => ({
							type: "image_url",
							image_url: { url: img.url, detail: "high" },
						})),
					];
				}
			}

			conversationMessages.push({
				role: "user",
				content: currentMessageContent,
			});

			// Create AI message placeholder for streaming
			const aiMessageId = Math.random().toString(36).substr(2, 9);
			const aiMessage: Message = {
				id: aiMessageId,
				type: "ai",
				content: "",
				timestamp: new Date(),
				isStreaming: true,
				analysis,
			};

			setMessages((prev) => [...prev, aiMessage]);

			// Stream response from API
			let fullResponse = "";
			const response = await fetch("/api/chat", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					messages: conversationMessages,
					stream: true,
					max_tokens: 1000,
					temperature: 0.7,
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to get AI response");
			}

			const reader = response.body?.getReader();
			if (!reader) {
				throw new Error("No response stream available");
			}

			const decoder = new TextDecoder();

			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				const chunk = decoder.decode(value);
				const lines = chunk.split("\n");

				for (const line of lines) {
					if (line.startsWith("data: ")) {
						const data = line.slice(6);
						if (data === "[DONE]") {
							break;
						}

						try {
							const parsed = JSON.parse(data);
							if (parsed.content) {
								fullResponse += parsed.content;
								setMessages((prev) =>
									prev.map((msg) =>
										msg.id === aiMessageId
											? { ...msg, content: fullResponse }
											: msg
									)
								);
							}
						} catch (e) {
							// Ignore parsing errors for incomplete chunks
						}
					}
				}
			}

			// Mark streaming as complete
			setMessages((prev) =>
				prev.map((msg) =>
					msg.id === aiMessageId ? { ...msg, isStreaming: false } : msg
				)
			);

			// Save AI response to database
			if (currentConversation) {
				try {
					await chatDatabaseService.createMessage({
						conversation_id: currentConversation.id,
						type: "ai",
						content: fullResponse,
						analysis,
					});
				} catch (dbError) {
					console.error("Failed to save AI response:", dbError);
				}
			}

			setIsTyping(false);
		} catch (error) {
			console.error("Error generating AI response:", error);

			// Show error message
			const errorMessage: Message = {
				id: Math.random().toString(36).substr(2, 9),
				type: "ai",
				content:
					"I apologize, but I encountered an error while processing your request. Please try again.",
				timestamp: new Date(),
			};

			setMessages((prev) => [...prev, errorMessage]);
			setIsTyping(false);
		}
	};

	const clearHistory = useCallback(async () => {
		if (currentConversation) {
			try {
				await chatDatabaseService.deleteConversation(currentConversation.id);
				setCurrentConversation(null);
				setMessages([]);
				// Create new conversation
				if (userId) {
					await initializeConversation();
				}
			} catch (error) {
				console.error("Failed to clear history:", error);
				setMessages([]);
			}
		} else {
			setMessages([]);
		}
	}, [currentConversation, userId, initializeConversation]);

	const searchMessages = useCallback(
		async (query: string) => {
			if (!userId) {
				console.log("Search requires user authentication");
				return;
			}

			try {
				const results = await chatDatabaseService.searchMessages(userId, query);
				console.log("Search results:", results);
				// You could emit these results to a search results state if needed
			} catch (error) {
				console.error("Search failed:", error);
			}
		},
		[userId]
	);

	const exportChat = useCallback(() => {
		const chatData = {
			conversation: currentConversation,
			messages,
			exportDate: new Date().toISOString(),
		};

		const blob = new Blob([JSON.stringify(chatData, null, 2)], {
			type: "application/json",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `chat-export-${new Date().toISOString().split("T")[0]}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	}, [messages, currentConversation]);

	const loadConversation = useCallback(
		async (conversationId: string) => {
			try {
				const conversation = await chatDatabaseService.getConversation(
					conversationId
				);
				if (conversation) {
					setCurrentConversation(conversation);
					await loadConversationMessages(conversationId);
				}
			} catch (error) {
				console.error("Failed to load conversation:", error);
			}
		},
		[loadConversationMessages]
	);

	return {
		messages,
		isTyping,
		sendMessage,
		clearHistory,
		searchMessages,
		exportChat,
		analyzeFiles,
		currentConversation,
		loadConversation,
		isInitialized,
	};
}
