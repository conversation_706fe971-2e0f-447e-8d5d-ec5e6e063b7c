"use client";

import { toast as sonnerToast } from "sonner";

type ToastProps = {
	title?: string;
	description?: string;
	variant?: "default" | "destructive";
};

function toast({ title, description, variant }: ToastProps) {
	if (variant === "destructive") {
		sonnerToast.error(title || "Error", {
			description,
		});
	} else {
		sonnerToast.success(title || "Success", {
			description,
		});
	}
}

function useToast() {
	return {
		toast,
		dismiss: () => sonnerToast.dismiss(),
	};
}

export { useToast, toast };
