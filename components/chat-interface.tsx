"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	Send,
	Paperclip,
	ImageIcon,
	FileText,
	Download,
	Search,
	Moon,
	Sun,
	Trash2,
	Sparkles,
	Bot,
	Loader2,
} from "lucide-react";
import { FileUpload } from "./file-upload";
import { MessageBubble } from "./message-bubble";
import { TypingIndicator } from "./typing-indicator";
import { SuggestedPrompts } from "./suggested-prompts";
import { QuickActions } from "./quick-actions";
import { ChatSearch } from "./chat-search";
import { useChat } from "../hooks/use-chat";
import { useTheme } from "../hooks/use-theme";
import { useAuth } from "../hooks/use-auth";

export interface Message {
	id: string;
	type: "user" | "ai" | "system";
	content: string;
	files?: UploadedFile[];
	timestamp: Date;
	isStreaming?: boolean;
	analysis?: FileAnalysis;
}

export interface UploadedFile {
	id: string;
	name: string;
	type: string;
	size: number;
	url: string;
	preview?: string;
	content?: string;
}

export interface FileAnalysis {
	summary?: string;
	keyPoints?: string[];
	extractedText?: string;
	imageDescription?: string;
	dataInsights?: string[];
	suggestions?: string[];
}

export function ChatInterface() {
	const { theme, toggleTheme } = useTheme();
	const { user } = useAuth();
	const {
		messages,
		isTyping,
		sendMessage,
		clearHistory,
		searchMessages,
		exportChat,
		analyzeFiles,
	} = useChat(user?.id);

	const [input, setInput] = useState("");
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [showFileUpload, setShowFileUpload] = useState(false);
	const [showSearch, setShowSearch] = useState(false);
	const [isAnalyzing, setIsAnalyzing] = useState(false);

	const messagesEndRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLInputElement>(null);

	// Auto-scroll to bottom when new messages arrive
	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [messages]);

	// Focus input on mount
	useEffect(() => {
		inputRef.current?.focus();
	}, []);

	const handleSendMessage = async () => {
		if (!input.trim() && uploadedFiles.length === 0) return;

		const messageContent = input.trim();
		const files = [...uploadedFiles];

		// Clear input and files
		setInput("");
		setUploadedFiles([]);
		setShowFileUpload(false);

		// Analyze files if present
		let analysis: FileAnalysis | undefined;
		if (files.length > 0) {
			setIsAnalyzing(true);
			try {
				analysis = await analyzeFiles(files);
			} catch (error) {
				console.error("Error analyzing files:", error);
			} finally {
				setIsAnalyzing(false);
			}
		}

		// Send message
		await sendMessage({
			content: messageContent || "Please analyze the uploaded files.",
			files,
			analysis,
		});

		// Focus back to input
		inputRef.current?.focus();
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	const handleFileUpload = (files: UploadedFile[]) => {
		setUploadedFiles((prev) => [...prev, ...files]);
		setShowFileUpload(false);
	};

	const removeFile = (fileId: string) => {
		setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
	};

	const handleSuggestedPrompt = (prompt: string) => {
		setInput(prompt);
		inputRef.current?.focus();
	};

	const handleQuickAction = async (action: string) => {
		if (uploadedFiles.length === 0) return;

		const actionPrompts = {
			summarize:
				"Please provide a comprehensive summary of the uploaded content.",
			keyPoints:
				"Extract the key points and main insights from the uploaded files.",
			questions: "Generate relevant questions I could ask about this content.",
			compare: "Compare and contrast the information in the uploaded files.",
			extract:
				"Extract all important data, numbers, and facts from the content.",
		};

		const prompt = actionPrompts[action as keyof typeof actionPrompts];
		if (prompt) {
			setInput(prompt);
			await handleSendMessage();
		}
	};

	const handlePasteImage = async (e: React.ClipboardEvent) => {
		const items = Array.from(e.clipboardData.items);
		const imageItems = items.filter((item) => item.type.startsWith("image/"));

		if (imageItems.length > 0) {
			const files: UploadedFile[] = [];

			for (const item of imageItems) {
				const file = item.getAsFile();
				if (file) {
					const url = URL.createObjectURL(file);
					const uploadedFile: UploadedFile = {
						id: Math.random().toString(36).substring(2, 11),
						name: `pasted-image-${Date.now()}.png`,
						type: file.type,
						size: file.size,
						url,
						preview: url,
					};
					files.push(uploadedFile);
				}
			}

			if (files.length > 0) {
				handleFileUpload(files);
			}
		}
	};

	return (
		<div
			className={`flex flex-col h-screen ${
				theme === "dark" ? "dark bg-gray-900" : "bg-gray-50"
			}`}
		>
			{/* Header */}
			<div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
							<Sparkles className="w-4 h-4 text-white" />
						</div>
						<div>
							<h1 className="text-lg font-semibold text-gray-900 dark:text-white">
								AI Assistant
							</h1>
							<p className="text-sm text-gray-500 dark:text-gray-400">
								Upload files and ask questions
							</p>
						</div>
					</div>

					<div className="flex items-center gap-2">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setShowSearch(!showSearch)}
							className="text-gray-600 dark:text-gray-300"
						>
							<Search className="w-4 h-4" />
						</Button>
						<Button
							variant="ghost"
							size="sm"
							onClick={toggleTheme}
							className="text-gray-600 dark:text-gray-300"
						>
							{theme === "dark" ? (
								<Sun className="w-4 h-4" />
							) : (
								<Moon className="w-4 h-4" />
							)}
						</Button>
						<Button
							variant="ghost"
							size="sm"
							onClick={exportChat}
							className="text-gray-600 dark:text-gray-300"
						>
							<Download className="w-4 h-4" />
						</Button>
						<Button
							variant="ghost"
							size="sm"
							onClick={clearHistory}
							className="text-gray-600 dark:text-gray-300"
						>
							<Trash2 className="w-4 h-4" />
						</Button>
					</div>
				</div>

				{/* Search Bar */}
				{showSearch && (
					<div className="mt-4">
						<ChatSearch
							onSearch={searchMessages}
							onClose={() => setShowSearch(false)}
						/>
					</div>
				)}
			</div>

			{/* Messages Area */}
			<div className="flex-1 flex flex-col min-h-0">
				<ScrollArea className="flex-1 p-4">
					<div className="max-w-4xl mx-auto space-y-4">
						{messages.length === 0 ? (
							<div className="text-center py-12">
								<div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
									<Bot className="w-8 h-8 text-white" />
								</div>
								<h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
									Welcome to AI Assistant
								</h2>
								<p className="text-gray-600 dark:text-gray-400 mb-6">
									Upload files (PDFs, images) and start a conversation. I can
									analyze, summarize, and answer questions about your content.
								</p>
								<SuggestedPrompts onSelectPrompt={handleSuggestedPrompt} />
							</div>
						) : (
							<>
								{messages.map((message) => (
									<MessageBubble key={message.id} message={message} />
								))}
								{isTyping && <TypingIndicator />}
								{isAnalyzing && (
									<div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
										<Loader2 className="w-4 h-4 animate-spin" />
										Analyzing uploaded files...
									</div>
								)}
							</>
						)}
						<div ref={messagesEndRef} />
					</div>
				</ScrollArea>

				{/* Quick Actions */}
				{uploadedFiles.length > 0 && (
					<div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
						<QuickActions onAction={handleQuickAction} />
					</div>
				)}

				{/* File Upload Area */}
				{showFileUpload && (
					<div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
						<FileUpload
							onUpload={handleFileUpload}
							onClose={() => setShowFileUpload(false)}
							maxFiles={5}
							maxSize={50 * 1024 * 1024} // 50MB
						/>
					</div>
				)}

				{/* Uploaded Files Preview */}
				{uploadedFiles.length > 0 && (
					<div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
						<div className="flex items-center gap-2 mb-3">
							<Paperclip className="w-4 h-4 text-gray-500" />
							<span className="text-sm font-medium text-gray-700 dark:text-gray-300">
								{uploadedFiles.length} file{uploadedFiles.length > 1 ? "s" : ""}{" "}
								attached
							</span>
						</div>
						<div className="flex flex-wrap gap-2">
							{uploadedFiles.map((file) => (
								<div
									key={file.id}
									className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-2 text-sm"
								>
									{file.type.startsWith("image/") ? (
										<ImageIcon className="w-4 h-4 text-blue-500" />
									) : (
										<FileText className="w-4 h-4 text-green-500" />
									)}
									<span className="text-gray-700 dark:text-gray-300 truncate max-w-32">
										{file.name}
									</span>
									<Button
										variant="ghost"
										size="sm"
										onClick={() => removeFile(file.id)}
										className="h-6 w-6 p-0 text-gray-500 hover:text-red-500"
									>
										×
									</Button>
								</div>
							))}
						</div>
					</div>
				)}

				{/* Input Area */}
				<div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4">
					<div className="max-w-4xl mx-auto">
						<div className="flex items-end gap-2">
							<div className="flex-1 relative">
								<Input
									ref={inputRef}
									value={input}
									onChange={(e) => setInput(e.target.value)}
									onKeyDown={handleKeyPress}
									onPaste={handlePasteImage}
									placeholder="Type a message or upload files..."
									className="pr-12 min-h-[44px] resize-none dark:bg-gray-700 dark:border-gray-600"
									disabled={isTyping || isAnalyzing}
								/>
								<Button
									variant="ghost"
									size="sm"
									onClick={() => setShowFileUpload(!showFileUpload)}
									className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
								>
									<Paperclip className="w-4 h-4" />
								</Button>
							</div>
							<Button
								onClick={handleSendMessage}
								disabled={
									(!input.trim() && uploadedFiles.length === 0) ||
									isTyping ||
									isAnalyzing
								}
								className="h-11 px-4 bg-blue-600 hover:bg-blue-700 text-white"
							>
								{isTyping || isAnalyzing ? (
									<Loader2 className="w-4 h-4 animate-spin" />
								) : (
									<Send className="w-4 h-4" />
								)}
							</Button>
						</div>
						<p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
							AI can make mistakes. Verify important information.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
