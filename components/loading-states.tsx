"use client";

import { <PERSON>ader<PERSON>, <PERSON><PERSON>, FileText, Image, Sparkles } from "lucide-react";

export function ChatLoadingState() {
	return (
		<div className="flex flex-col items-center justify-center min-h-[400px] p-8">
			<div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
				<Bot className="w-8 h-8 text-white" />
			</div>

			<div className="flex items-center gap-2 mb-2">
				<Loader2 className="w-4 h-4 animate-spin text-blue-600" />
				<span className="text-lg font-medium text-gray-900 dark:text-white">
					Initializing Chat
				</span>
			</div>

			<p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
				Setting up your conversation and connecting to AI services...
			</p>
		</div>
	);
}

export function FileAnalysisLoadingState({ fileName }: { fileName?: string }) {
	return (
		<div className="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
			<div className="flex-shrink-0">
				<div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
					<Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400" />
				</div>
			</div>

			<div className="flex-1">
				<div className="flex items-center gap-2 mb-1">
					<Loader2 className="w-4 h-4 animate-spin text-blue-600" />
					<span className="text-sm font-medium text-blue-900 dark:text-blue-100">
						Analyzing {fileName ? `"${fileName}"` : "files"}...
					</span>
				</div>
				<p className="text-xs text-blue-700 dark:text-blue-300">
					AI is processing your content to extract insights and key information
				</p>
			</div>
		</div>
	);
}

export function UploadingFileState({
	fileName,
	progress,
}: {
	fileName: string;
	progress?: number;
}) {
	return (
		<div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
			<div className="flex-shrink-0">
				<div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
					<FileText className="w-4 h-4 text-gray-600 dark:text-gray-400" />
				</div>
			</div>

			<div className="flex-1 min-w-0">
				<div className="flex items-center gap-2 mb-1">
					<Loader2 className="w-3 h-3 animate-spin text-gray-600" />
					<span className="text-sm font-medium text-gray-900 dark:text-white truncate">
						Uploading {fileName}
					</span>
				</div>

				{progress !== undefined && (
					<div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
						<div
							className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
							style={{ width: `${progress}%` }}
						/>
					</div>
				)}

				{progress === undefined && (
					<div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
						<div
							className="bg-blue-600 h-1.5 rounded-full animate-pulse"
							style={{ width: "60%" }}
						/>
					</div>
				)}
			</div>
		</div>
	);
}

export function AIThinkingState() {
	return (
		<div className="flex items-center gap-3 p-4">
			<div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
				<Bot className="w-4 h-4 text-white" />
			</div>

			<div className="flex items-center gap-2">
				<div className="flex space-x-1">
					<div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
					<div
						className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
						style={{ animationDelay: "0.1s" }}
					></div>
					<div
						className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
						style={{ animationDelay: "0.2s" }}
					></div>
				</div>
				<span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
					AI is thinking...
				</span>
			</div>
		</div>
	);
}

export function ConnectionErrorState({ onRetry }: { onRetry: () => void }) {
	return (
		<div className="flex flex-col items-center justify-center p-8 text-center">
			<div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
				<Bot className="w-6 h-6 text-red-600 dark:text-red-400" />
			</div>

			<h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
				Connection Error
			</h3>

			<p className="text-gray-600 dark:text-gray-400 mb-4 max-w-sm">
				Unable to connect to AI services. Please check your configuration and
				try again.
			</p>

			<button
				onClick={onRetry}
				className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
			>
				Retry Connection
			</button>
		</div>
	);
}

export function EmptyStateWithSetup() {
	return (
		<div className="text-center py-12">
			<div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
				<Bot className="w-8 h-8 text-white" />
			</div>

			<h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
				Welcome to AI Assistant
			</h2>

			<p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
				Upload files (PDFs, images) and start a conversation. I can analyze,
				summarize, and answer questions about your content.
			</p>

			<div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
				<div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
					<Image
						className="w-8 h-8 text-blue-500 mx-auto mb-2"
						alt="Image analysis icon"
					/>
					<h3 className="font-medium text-gray-900 dark:text-white mb-1">
						Image Analysis
					</h3>
					<p className="text-sm text-gray-600 dark:text-gray-400">
						Upload images for detailed analysis and description
					</p>
				</div>

				<div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
					<FileText className="w-8 h-8 text-green-500 mx-auto mb-2" />
					<h3 className="font-medium text-gray-900 dark:text-white mb-1">
						Document Processing
					</h3>
					<p className="text-sm text-gray-600 dark:text-gray-400">
						Extract insights from PDFs and text documents
					</p>
				</div>

				<div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
					<Sparkles className="w-8 h-8 text-purple-500 mx-auto mb-2" />
					<h3 className="font-medium text-gray-900 dark:text-white mb-1">
						AI Conversations
					</h3>
					<p className="text-sm text-gray-600 dark:text-gray-400">
						Ask questions and get intelligent responses
					</p>
				</div>
			</div>
		</div>
	);
}
