"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, User, Copy, ImageIcon, FileText, ChevronDown, ChevronUp, Sparkles, FileSearch } from "lucide-react"
import type { Message } from "./chat-interface"

interface MessageBubbleProps {
  message: Message
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const [showAnalysis, setShowAnalysis] = useState(false)
  const [showFiles, setShowFiles] = useState(true)

  const isUser = message.type === "user"
  const isSystem = message.type === "system"

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (error) {
      console.error("Failed to copy:", error)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith("image/")) {
      return <ImageIcon className="w-4 h-4 text-blue-500" />
    }
    return <FileText className="w-4 h-4 text-green-500" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  if (isSystem) {
    return (
      <div className="flex justify-center">
        <Badge variant="secondary" className="text-xs">
          {message.content}
        </Badge>
      </div>
    )
  }

  return (
    <div className={`flex gap-3 ${isUser ? "flex-row-reverse" : "flex-row"}`}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        <div
          className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? "bg-blue-600 text-white" : "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
          }`}
        >
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </div>
      </div>

      {/* Message Content */}
      <div className={`flex-1 max-w-3xl ${isUser ? "text-right" : "text-left"}`}>
        <div
          className={`inline-block p-4 rounded-2xl ${
            isUser
              ? "bg-blue-600 text-white"
              : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white"
          }`}
        >
          {/* Files Preview */}
          {message.files && message.files.length > 0 && (
            <div className="mb-3">
              <div className="flex items-center gap-2 mb-2">
                <FileSearch className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {message.files.length} file{message.files.length > 1 ? "s" : ""} attached
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFiles(!showFiles)}
                  className={`h-6 w-6 p-0 ${isUser ? "text-white/70 hover:text-white" : ""}`}
                >
                  {showFiles ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
                </Button>
              </div>

              {showFiles && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {message.files.map((file) => (
                    <div
                      key={file.id}
                      className={`p-2 rounded-lg border ${
                        isUser
                          ? "bg-white/10 border-white/20"
                          : "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        {getFileIcon(file.type)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{file.name}</p>
                          <p className={`text-xs ${isUser ? "text-white/70" : "text-gray-500"}`}>
                            {formatFileSize(file.size)}
                          </p>
                        </div>
                        {file.preview && (
                          <img
                            src={file.preview || "/placeholder.svg"}
                            alt={file.name}
                            className="w-8 h-8 object-cover rounded"
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Message Text */}
          {message.content && (
            <div className="prose prose-sm max-w-none">
              <p className="whitespace-pre-wrap">{message.content}</p>
            </div>
          )}

          {/* AI Analysis Results */}
          {message.analysis && !isUser && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium">AI Analysis</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAnalysis(!showAnalysis)}
                  className="h-6 w-6 p-0"
                >
                  {showAnalysis ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
                </Button>
              </div>

              {showAnalysis && (
                <div className="space-y-3 text-sm">
                  {message.analysis.summary && (
                    <div>
                      <h4 className="font-medium mb-1">Summary</h4>
                      <p className="text-gray-600 dark:text-gray-300">{message.analysis.summary}</p>
                    </div>
                  )}

                  {message.analysis.keyPoints && message.analysis.keyPoints.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-1">Key Points</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-300">
                        {message.analysis.keyPoints.map((point, index) => (
                          <li key={index}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {message.analysis.imageDescription && (
                    <div>
                      <h4 className="font-medium mb-1">Image Description</h4>
                      <p className="text-gray-600 dark:text-gray-300">{message.analysis.imageDescription}</p>
                    </div>
                  )}

                  {message.analysis.dataInsights && message.analysis.dataInsights.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-1">Data Insights</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-300">
                        {message.analysis.dataInsights.map((insight, index) => (
                          <li key={index}>{insight}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {message.analysis.suggestions && message.analysis.suggestions.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-1">Suggestions</h4>
                      <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-300">
                        {message.analysis.suggestions.map((suggestion, index) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Streaming Indicator */}
          {message.isStreaming && (
            <div className="flex items-center gap-2 mt-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                ></div>
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* Message Actions */}
        <div className={`flex items-center gap-2 mt-2 ${isUser ? "justify-end" : "justify-start"}`}>
          <span className="text-xs text-gray-500 dark:text-gray-400">{formatTime(message.timestamp)}</span>
          {!isUser && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(message.content)}
              className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Copy className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
