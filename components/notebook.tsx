"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/hooks/use-toast"
import {
  BookOpen,
  Plus,
  Search,
  Filter,
  Calendar,
  Tag,
  Image as ImageIcon,
  FileText,
  TrendingUp,
  Target,
  AlertTriangle,
  Star,
  Edit,
  Trash2,
  MoreHorizontal,
  Upload,
  X,
  Save,
  Eye,
  <PERSON>Off,
} from "lucide-react"
import type { User } from "@supabase/supabase-js"

interface NotebookEntry {
  id: string
  title: string
  content: string
  category: "strategy" | "rules" | "analysis" | "learning" | "watchlist" | "journal"
  tags: string[]
  images: string[]
  createdAt: string
  updatedAt: string
  isPrivate: boolean
  symbol?: string
  entryDate?: string
  rating?: number
}

interface NotebookProps {
  user: User | null
}

const categoryConfig = {
  strategy: { label: "Trading Strategy", icon: Target, color: "bg-blue-100 text-blue-800" },
  rules: { label: "Trading Rules", icon: AlertTriangle, color: "bg-red-100 text-red-800" },
  analysis: { label: "Market Analysis", icon: TrendingUp, color: "bg-green-100 text-green-800" },
  learning: { label: "Learning Notes", icon: BookOpen, color: "bg-purple-100 text-purple-800" },
  watchlist: { label: "Stock Watchlist", icon: Eye, color: "bg-yellow-100 text-yellow-800" },
  journal: { label: "Trade Journal", icon: FileText, color: "bg-gray-100 text-gray-800" },
}

export function Notebook({ user }: NotebookProps) {
  const [entries, setEntries] = useState<NotebookEntry[]>([])
  const [isCreating, setIsCreating] = useState(false)
  const [editingEntry, setEditingEntry] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedTag, setSelectedTag] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [newEntry, setNewEntry] = useState<Partial<NotebookEntry>>({
    title: "",
    content: "",
    category: "journal",
    tags: [],
    images: [],
    isPrivate: false,
    symbol: "",
    rating: 0,
  })

  // Mock data for demonstration
  useEffect(() => {
    const mockEntries: NotebookEntry[] = [
      {
        id: "1",
        title: "AAPL Breakout Strategy",
        content: "Apple showing strong momentum above $180 resistance. Key levels to watch: Support at $175, Resistance at $185. Volume confirmation needed for continuation.",
        category: "analysis",
        tags: ["AAPL", "breakout", "momentum"],
        images: [],
        createdAt: "2024-01-15T10:30:00Z",
        updatedAt: "2024-01-15T10:30:00Z",
        isPrivate: false,
        symbol: "AAPL",
        rating: 4,
      },
      {
        id: "2",
        title: "Risk Management Rules",
        content: "1. Never risk more than 2% per trade\n2. Always set stop loss before entry\n3. Take profits at 2:1 risk/reward minimum\n4. No more than 3 positions in same sector",
        category: "rules",
        tags: ["risk-management", "discipline"],
        images: [],
        createdAt: "2024-01-14T09:15:00Z",
        updatedAt: "2024-01-14T09:15:00Z",
        isPrivate: true,
        rating: 5,
      },
    ]
    setEntries(mockEntries)
  }, [])

  const handleCreateEntry = () => {
    if (!newEntry.title || !newEntry.content) {
      toast({
        title: "Error",
        description: "Please fill in title and content",
        variant: "destructive",
      })
      return
    }

    const entry: NotebookEntry = {
      id: Date.now().toString(),
      title: newEntry.title!,
      content: newEntry.content!,
      category: newEntry.category as NotebookEntry["category"],
      tags: newEntry.tags || [],
      images: newEntry.images || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPrivate: newEntry.isPrivate || false,
      symbol: newEntry.symbol,
      rating: newEntry.rating,
    }

    setEntries([entry, ...entries])
    setNewEntry({
      title: "",
      content: "",
      category: "journal",
      tags: [],
      images: [],
      isPrivate: false,
      symbol: "",
      rating: 0,
    })
    setIsCreating(false)

    toast({
      title: "Success",
      description: "Notebook entry created successfully",
    })
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    // In a real app, you'd upload to your storage service
    Array.from(files).forEach((file) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        setNewEntry((prev) => ({
          ...prev,
          images: [...(prev.images || []), imageUrl],
        }))
      }
      reader.readAsDataURL(file)
    })
  }

  const removeImage = (index: number) => {
    setNewEntry((prev) => ({
      ...prev,
      images: prev.images?.filter((_, i) => i !== index) || [],
    }))
  }

  const addTag = (tag: string) => {
    if (tag && !newEntry.tags?.includes(tag)) {
      setNewEntry((prev) => ({
        ...prev,
        tags: [...(prev.tags || []), tag],
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setNewEntry((prev) => ({
      ...prev,
      tags: prev.tags?.filter((tag) => tag !== tagToRemove) || [],
    }))
  }

  const deleteEntry = (entryId: string) => {
    if (confirm("Are you sure you want to delete this entry?")) {
      setEntries(entries.filter((entry) => entry.id !== entryId))
      toast({
        title: "Success",
        description: "Entry deleted successfully",
      })
    }
  }

  const filteredEntries = entries.filter((entry) => {
    const matchesSearch = entry.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || entry.category === selectedCategory
    const matchesTag = selectedTag === "all" || entry.tags.includes(selectedTag)
    
    return matchesSearch && matchesCategory && matchesTag
  })

  const allTags = Array.from(new Set(entries.flatMap(entry => entry.tags)))

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
      />
    ))
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <BookOpen className="w-6 h-6" />
            Trading Notebook
          </h2>
          <p className="text-sm text-gray-600">Document your trading strategies, rules, and insights</p>
        </div>
        <Button onClick={() => setIsCreating(true)} className="gap-2">
          <Plus className="w-4 h-4" />
          New Entry
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search entries, tags, or symbols..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {Object.entries(categoryConfig).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    {config.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedTag} onValueChange={setSelectedTag}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Tags" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tags</SelectItem>
                {allTags.map((tag) => (
                  <SelectItem key={tag} value={tag}>
                    {tag}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Create New Entry Modal */}
      {isCreating && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Create New Entry
              <Button variant="ghost" size="sm" onClick={() => setIsCreating(false)}>
                <X className="w-4 h-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={newEntry.title || ""}
                  onChange={(e) => setNewEntry({ ...newEntry, title: e.target.value })}
                  placeholder="Entry title..."
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newEntry.category}
                  onValueChange={(value) => setNewEntry({ ...newEntry, category: value as NotebookEntry["category"] })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(categoryConfig).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="symbol">Symbol (Optional)</Label>
                <Input
                  id="symbol"
                  value={newEntry.symbol || ""}
                  onChange={(e) => setNewEntry({ ...newEntry, symbol: e.target.value.toUpperCase() })}
                  placeholder="AAPL, TSLA, etc."
                />
              </div>
              <div>
                <Label>Rating</Label>
                <div className="flex gap-1 mt-1">
                  {Array.from({ length: 5 }, (_, i) => (
                    <button
                      key={i}
                      type="button"
                      onClick={() => setNewEntry({ ...newEntry, rating: i + 1 })}
                    >
                      <Star
                        className={`w-5 h-5 ${i < (newEntry.rating || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                      />
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="content">Content</Label>
              <textarea
                id="content"
                value={newEntry.content || ""}
                onChange={(e) => setNewEntry({ ...newEntry, content: e.target.value })}
                placeholder="Write your trading notes, strategies, rules, or analysis..."
                className="w-full h-32 p-3 border rounded-md resize-none"
              />
            </div>

            <div>
              <Label>Tags</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {newEntry.tags?.map((tag) => (
                  <Badge key={tag} variant="secondary" className="gap-1">
                    {tag}
                    <button onClick={() => removeTag(tag)}>
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
              <Input
                placeholder="Add tags (press Enter)"
                className="mt-2"
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault()
                    const target = e.target as HTMLInputElement
                    addTag(target.value.trim())
                    target.value = ""
                  }
                }}
              />
            </div>

            <div>
              <Label>Images</Label>
              <div className="mt-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="gap-2"
                >
                  <Upload className="w-4 h-4" />
                  Upload Images
                </Button>
              </div>
              {newEntry.images && newEntry.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-4">
                  {newEntry.images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-24 object-cover rounded border"
                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="private"
                checked={newEntry.isPrivate || false}
                onChange={(e) => setNewEntry({ ...newEntry, isPrivate: e.target.checked })}
              />
              <Label htmlFor="private" className="flex items-center gap-2">
                {newEntry.isPrivate ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                Private Entry
              </Label>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleCreateEntry} className="gap-2">
                <Save className="w-4 h-4" />
                Save Entry
              </Button>
              <Button variant="outline" onClick={() => setIsCreating(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Entries List */}
      <div className="grid gap-4">
        {filteredEntries.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No entries found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || selectedCategory !== "all" || selectedTag !== "all"
                  ? "Try adjusting your search or filters"
                  : "Start documenting your trading journey"}
              </p>
              {!isCreating && (
                <Button onClick={() => setIsCreating(true)} className="gap-2">
                  <Plus className="w-4 h-4" />
                  Create First Entry
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredEntries.map((entry) => {
            const categoryInfo = categoryConfig[entry.category]
            const CategoryIcon = categoryInfo.icon

            return (
              <Card key={entry.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={categoryInfo.color}>
                          <CategoryIcon className="w-3 h-3 mr-1" />
                          {categoryInfo.label}
                        </Badge>
                        {entry.symbol && (
                          <Badge variant="outline">{entry.symbol}</Badge>
                        )}
                        {entry.isPrivate && (
                          <Badge variant="secondary">
                            <EyeOff className="w-3 h-3 mr-1" />
                            Private
                          </Badge>
                        )}
                      </div>
                      <CardTitle className="text-lg">{entry.title}</CardTitle>
                      {entry.rating && entry.rating > 0 && (
                        <div className="flex gap-1 mt-1">
                          {renderStars(entry.rating)}
                        </div>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setEditingEntry(entry.id)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => deleteEntry(entry.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="whitespace-pre-wrap text-gray-700">
                      {entry.content}
                    </div>

                    {entry.images && entry.images.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {entry.images.map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Entry image ${index + 1}`}
                            className="w-full h-24 object-cover rounded border cursor-pointer hover:opacity-80"
                            onClick={() => window.open(image, "_blank")}
                          />
                        ))}
                      </div>
                    )}

                    {entry.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {entry.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            <Tag className="w-3 h-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    <div className="text-xs text-gray-500 border-t pt-2">
                      Created: {new Date(entry.createdAt).toLocaleDateString()}
                      {entry.updatedAt !== entry.createdAt && (
                        <span className="ml-4">
                          Updated: {new Date(entry.updatedAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </div>
    </div>
  )
}
