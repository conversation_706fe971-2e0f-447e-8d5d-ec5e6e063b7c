"use client";

import type React from "react";

import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import {
	Upload,
	FileText,
	ImageIcon,
	X,
	AlertCircle,
	CheckCircle,
} from "lucide-react";
import type { UploadedFile } from "./chat-interface";

interface FileUploadProps {
	onUpload: (files: UploadedFile[]) => void;
	onClose: () => void;
	maxFiles?: number;
	maxSize?: number;
	acceptedTypes?: string[];
}

export function FileUpload({
	onUpload,
	onClose,
	maxFiles = 5,
	maxSize = 50 * 1024 * 1024, // 50MB
	acceptedTypes = [
		"application/pdf",
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/svg+xml",
		"text/plain",
		"text/csv",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	],
}: FileUploadProps) {
	const [isDragging, setIsDragging] = useState(false);
	const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
		{}
	);
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [errors, setErrors] = useState<string[]>([]);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const validateFile = (file: File): string | null => {
		if (!acceptedTypes.includes(file.type)) {
			return `File type ${file.type} is not supported`;
		}
		if (file.size > maxSize) {
			return `File size exceeds ${Math.round(maxSize / (1024 * 1024))}MB limit`;
		}
		return null;
	};

	// Helper function to convert file to base64
	const fileToBase64 = (file: File): Promise<string> => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result as string);
			reader.onerror = (error) => reject(error);
		});
	};

	const processFile = async (file: File): Promise<UploadedFile> => {
		const fileId = Math.random().toString(36).substr(2, 9);

		// Create object URL for preview (local display)
		const previewUrl = URL.createObjectURL(file);

		// Extract text content for text files
		let content: string | undefined;
		let preview: string | undefined;
		let url: string = previewUrl; // Default to blob URL

		if (file.type.startsWith("text/")) {
			content = await file.text();
		} else if (file.type === "application/pdf") {
			// In a real app, you'd use a PDF parsing library like pdf-parse
			content = "PDF content extraction would happen here";
		} else if (file.type.startsWith("image/")) {
			preview = previewUrl;
			// Convert image to base64 for Azure OpenAI compatibility
			try {
				url = await fileToBase64(file);
			} catch (error) {
				console.error("Failed to convert image to base64:", error);
				// Fallback to blob URL
				url = previewUrl;
			}
		}

		return {
			id: fileId,
			name: file.name,
			type: file.type,
			size: file.size,
			url, // This will be base64 for images, blob URL for others
			preview,
			content,
		};
	};

	const handleFiles = async (files: FileList) => {
		const fileArray = Array.from(files);
		const newErrors: string[] = [];
		const validFiles: File[] = [];

		// Validate files
		for (const file of fileArray) {
			const error = validateFile(file);
			if (error) {
				newErrors.push(`${file.name}: ${error}`);
			} else {
				validFiles.push(file);
			}
		}

		// Check total file count
		if (uploadedFiles.length + validFiles.length > maxFiles) {
			newErrors.push(`Maximum ${maxFiles} files allowed`);
			setErrors(newErrors);
			return;
		}

		setErrors(newErrors);

		// Process valid files
		const processedFiles: UploadedFile[] = [];
		for (const file of validFiles) {
			const fileId = Math.random().toString(36).substr(2, 9);

			// Simulate upload progress
			setUploadProgress((prev) => ({ ...prev, [fileId]: 0 }));

			try {
				// Simulate upload delay
				for (let i = 0; i <= 100; i += 10) {
					await new Promise((resolve) => setTimeout(resolve, 50));
					setUploadProgress((prev) => ({ ...prev, [fileId]: i }));
				}

				const processedFile = await processFile(file);
				processedFiles.push({ ...processedFile, id: fileId });

				setUploadProgress((prev) => {
					const newProgress = { ...prev };
					delete newProgress[fileId];
					return newProgress;
				});
			} catch (error) {
				console.error("Error processing file:", error);
				newErrors.push(`Failed to process ${file.name}`);
				setErrors([...newErrors]);
			}
		}

		setUploadedFiles((prev) => [...prev, ...processedFiles]);
	};

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			setIsDragging(false);

			const files = e.dataTransfer.files;
			if (files.length > 0) {
				handleFiles(files);
			}
		},
		[handleFiles]
	);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragging(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragging(false);
	}, []);

	const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (files) {
			handleFiles(files);
		}
	};

	const removeFile = (fileId: string) => {
		setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
	};

	const handleUpload = () => {
		if (uploadedFiles.length > 0) {
			onUpload(uploadedFiles);
			setUploadedFiles([]);
			setErrors([]);
		}
	};

	const formatFileSize = (bytes: number) => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return (
			Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
		);
	};

	const getFileIcon = (type: string) => {
		if (type.startsWith("image/")) {
			return <ImageIcon className="w-5 h-5 text-blue-500" />;
		}
		return <FileText className="w-5 h-5 text-green-500" />;
	};

	return (
		<Card className="w-full">
			<CardContent className="p-6">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold">Upload Files</h3>
					<Button variant="ghost" size="sm" onClick={onClose}>
						<X className="w-4 h-4" />
					</Button>
				</div>

				{/* Drop Zone */}
				<div
					onDrop={handleDrop}
					onDragOver={handleDragOver}
					onDragLeave={handleDragLeave}
					onClick={() => fileInputRef.current?.click()}
					className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${
							isDragging
								? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
								: "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
						}
          `}
				>
					<Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
					<p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
						Drop files here or click to browse
					</p>
					<p className="text-sm text-gray-500 dark:text-gray-400">
						Supports PDF, images, text files up to{" "}
						{Math.round(maxSize / (1024 * 1024))}MB
					</p>
				</div>

				<input
					ref={fileInputRef}
					type="file"
					multiple
					accept={acceptedTypes.join(",")}
					onChange={handleFileSelect}
					className="hidden"
				/>

				{/* Upload Progress */}
				{Object.keys(uploadProgress).length > 0 && (
					<div className="mt-4 space-y-2">
						{Object.entries(uploadProgress).map(([fileId, progress]) => (
							<div key={fileId} className="space-y-1">
								<div className="flex items-center justify-between text-sm">
									<span>Uploading...</span>
									<span>{progress}%</span>
								</div>
								<Progress value={progress} className="h-2" />
							</div>
						))}
					</div>
				)}

				{/* Uploaded Files */}
				{uploadedFiles.length > 0 && (
					<div className="mt-4 space-y-2">
						<h4 className="font-medium text-gray-700 dark:text-gray-300">
							Uploaded Files ({uploadedFiles.length}/{maxFiles})
						</h4>
						{uploadedFiles.map((file) => (
							<div
								key={file.id}
								className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
							>
								<div className="flex items-center gap-3">
									{getFileIcon(file.type)}
									<div>
										<p className="font-medium text-gray-700 dark:text-gray-300">
											{file.name}
										</p>
										<p className="text-sm text-gray-500 dark:text-gray-400">
											{formatFileSize(file.size)}
										</p>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<CheckCircle className="w-4 h-4 text-green-500" />
									<Button
										variant="ghost"
										size="sm"
										onClick={() => removeFile(file.id)}
										className="text-red-500 hover:text-red-700"
									>
										<X className="w-4 h-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}

				{/* Errors */}
				{errors.length > 0 && (
					<div className="mt-4 space-y-2">
						{errors.map((error, index) => (
							<div
								key={index}
								className="flex items-center gap-2 text-red-600 dark:text-red-400 text-sm"
							>
								<AlertCircle className="w-4 h-4" />
								{error}
							</div>
						))}
					</div>
				)}

				{/* Actions */}
				<div className="flex justify-end gap-2 mt-6">
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleUpload}
						disabled={uploadedFiles.length === 0}
						className="bg-blue-600 hover:bg-blue-700 text-white"
					>
						Upload {uploadedFiles.length} file
						{uploadedFiles.length !== 1 ? "s" : ""}
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
