"use client"
import { Card, CardContent } from "@/components/ui/card"
import { FileText, ImageIcon, BarChart3, MessageSquare, Search, Lightbulb } from "lucide-react"

interface SuggestedPromptsProps {
  onSelectPrompt: (prompt: string) => void
}

export function SuggestedPrompts({ onSelectPrompt }: SuggestedPromptsProps) {
  const prompts = [
    {
      icon: <FileText className="w-5 h-5" />,
      title: "Analyze Document",
      description: "Upload a PDF and get key insights",
      prompt: "Please analyze this document and provide a comprehensive summary with key points.",
    },
    {
      icon: <ImageIcon className="w-5 h-5" />,
      title: "Describe Image",
      description: "Get detailed image descriptions",
      prompt: "Please describe what you see in this image in detail.",
    },
    {
      icon: <BarChart3 className="w-5 h-5" />,
      title: "Extract Data",
      description: "Pull data from charts and tables",
      prompt: "Extract all numerical data and create a summary of the key metrics shown.",
    },
    {
      icon: <MessageSquare className="w-5 h-5" />,
      title: "Ask Questions",
      description: "Generate relevant questions",
      prompt: "Generate 5 thoughtful questions I could ask about this content.",
    },
    {
      icon: <Search className="w-5 h-5" />,
      title: "Find Information",
      description: "Search for specific details",
      prompt: "Help me find specific information in these documents. What should I look for?",
    },
    {
      icon: <Lightbulb className="w-5 h-5" />,
      title: "Get Insights",
      description: "Discover hidden patterns",
      prompt: "What insights, patterns, or trends can you identify in this content?",
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
      {prompts.map((prompt, index) => (
        <Card
          key={index}
          className="cursor-pointer hover:shadow-md transition-shadow border-gray-200 dark:border-gray-700"
          onClick={() => onSelectPrompt(prompt.prompt)}
        >
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-blue-600 dark:text-blue-400 mt-1">{prompt.icon}</div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">{prompt.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{prompt.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
