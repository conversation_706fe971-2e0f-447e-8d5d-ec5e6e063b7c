"use client";

import { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";

interface Trade {
	id: string;
	symbol: string;
	trade_date: string;
	image_url?: string;
	ai_analysis?: string | Record<string, unknown>;
}

interface EditTradeDialogProps {
	trade: Trade | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSave: (
		tradeId: string,
		data: { symbol: string; trade_date: string }
	) => Promise<void>;
}

export function EditTradeDialog({
	trade,
	open,
	onOpenChange,
	onSave,
}: EditTradeDialogProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [formData, setFormData] = useState({
		symbol: trade?.symbol || "",
		trade_date: trade?.trade_date || "",
	});

	const handleSave = async () => {
		if (!trade) return;

		setIsLoading(true);
		try {
			await onSave(trade.id, formData);
			onOpenChange(false);
		} catch (error) {
			console.error("Error saving trade:", error);
		} finally {
			setIsLoading(false);
		}
	};

	// Update form data when trade changes
	if (
		trade &&
		(formData.symbol !== trade.symbol ||
			formData.trade_date !== trade.trade_date)
	) {
		setFormData({
			symbol: trade.symbol,
			trade_date: trade.trade_date,
		});
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Edit Trade</DialogTitle>
				</DialogHeader>
				<div className="space-y-4">
					<div>
						<Label htmlFor="edit-symbol">Symbol</Label>
						<Input
							id="edit-symbol"
							value={formData.symbol}
							onChange={(e) =>
								setFormData((prev) => ({ ...prev, symbol: e.target.value }))
							}
							placeholder="e.g., AAPL"
						/>
					</div>
					<div>
						<Label htmlFor="edit-date">Date</Label>
						<Input
							id="edit-date"
							type="date"
							value={formData.trade_date}
							onChange={(e) =>
								setFormData((prev) => ({ ...prev, trade_date: e.target.value }))
							}
						/>
					</div>
					<div className="flex justify-end gap-2">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={isLoading}>
							{isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
							Save Changes
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
