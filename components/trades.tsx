"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/hooks/use-toast"
import {
  Upload,
  ImageIcon,
  Loader2,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Eye,
  MoreHorizontal,
  Edit,
  Trash2,
  Share,
  Copy,
  ExternalLink,
} from "lucide-react"
import { isSupabaseConfigured } from "../lib/supabase"
import { setupStorageBucket } from "../app/actions/setup-storage"
import {
  createTrade,
  getTrades,
  updateTrade,
  deleteTrade,
  shareTrade,
  unshareTrade,
  updateTradeAnalysis,
} from "../app/actions/trades"
import { uploadImageToSupabase } from "../app/actions/upload-image"
import { TradeTableSkeleton } from "./trade-skeleton"
import { EditTradeDialog } from "./edit-trade-dialog"
import type { User } from "@supabase/supabase-js"

interface Trade {
  id: string
  symbol: string
  trade_date: string
  image_url?: string
  ai_analysis?: {
    sentiment: "bullish" | "bearish" | "neutral"
    confidence: number
    keyPoints: string[]
    recommendation: string
  }
  is_shared?: boolean
  share_token?: string
  isAnalyzing?: boolean
  created_at?: string
}

interface TradesProps {
  user: User | null
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export function Trades({ user }: TradesProps) {
  const [trades, setTrades] = useState<Trade[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isSettingUp, setIsSettingUp] = useState(false)
  const [setupMessage, setSetupMessage] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  })
  const [selectedTrade, setSelectedTrade] = useState<Trade | null>(null)
  const [editingTrade, setEditingTrade] = useState<Trade | null>(null)
  const [newTrade, setNewTrade] = useState({
    symbol: "",
    date: new Date().toISOString().split("T")[0],
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (user) {
      loadTrades(1)
      if (isSupabaseConfigured) {
        handleSetupStorage()
      }
    }
  }, [user])

  const loadTrades = async (page: number = pagination.page) => {
    if (!user) return

    setIsLoading(true)
    try {
      if (isSupabaseConfigured) {
        const result = await getTrades(user.id, { page, limit: pagination.limit })
        if (result.success && result.data && result.pagination) {
          setTrades(result.data)
          setPagination(result.pagination)
        }
      } else {
        // Mock data for demo
        const mockTrades: Trade[] = [
          {
            id: "1",
            symbol: "AAPL",
            trade_date: "2024-01-15",
            image_url: "/placeholder.svg?height=96&width=128",
            is_shared: false,
            ai_analysis: {
              sentiment: "bullish",
              confidence: 85,
              keyPoints: ["Strong support level", "High volume"],
              recommendation: "Consider entering position",
            },
          },
          {
            id: "2",
            symbol: "TSLA",
            trade_date: "2024-01-14",
            image_url: "/placeholder.svg?height=96&width=128",
            is_shared: true,
            share_token: "demo-token",
            ai_analysis: {
              sentiment: "bearish",
              confidence: 72,
              keyPoints: ["Resistance at current level", "Declining volume"],
              recommendation: "Wait for better entry",
            },
          },
        ]
        setTrades(mockTrades)
        setPagination({
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        })
      }
    } catch (error) {
      console.error("Error loading trades:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSetupStorage = async () => {
    if (!isSupabaseConfigured) return

    setIsSettingUp(true)
    try {
      const result = await setupStorageBucket()
      if (result.success) {
        setSetupMessage("✅ Storage ready")
      } else {
        setSetupMessage(`❌ Setup failed: ${result.error}`)
      }
    } catch (error) {
      setSetupMessage("❌ Setup failed")
    } finally {
      setIsSettingUp(false)
    }
  }

  const uploadImage = async (file: File): Promise<string> => {
    if (!isSupabaseConfigured || !user) {
      // Mock upload for demo purposes
      console.log("Using mock upload")
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockUrl = URL.createObjectURL(file)
          resolve(mockUrl)
        }, 1000)
      })
    }

    try {
      // Use server action to upload (bypasses RLS issues)
      const formData = new FormData()
      formData.append("file", file)

      const result = await uploadImageToSupabase(formData, user.id)

      if (!result.success) {
        throw new Error(result.error)
      }

      return result.url
    } catch (error) {
      console.error("Error in uploadImage:", error)

      // Fallback to mock upload for demo
      console.log("Falling back to mock upload")
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockUrl = URL.createObjectURL(file)
          resolve(mockUrl)
        }, 1000)
      })
    }
  }

  const analyzeImage = async (imageUrl: string) => {
    // Simulate AI analysis
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const sentiments = ["bullish", "bearish", "neutral"] as const
    const mockAnalysis = {
      sentiment: sentiments[Math.floor(Math.random() * sentiments.length)],
      confidence: Math.floor(Math.random() * 30) + 70,
      keyPoints: [
        "Strong support level identified at current price",
        "Volume indicates institutional interest",
        "Technical indicators suggest momentum shift",
        "Risk/reward ratio appears favorable",
      ],
      recommendation:
        Math.random() > 0.5
          ? "Consider entering position with tight stop loss"
          : "Wait for better entry point or confirmation",
    }

    return mockAnalysis
  }

  const handleImageUpload = async (file: File) => {
    if (!newTrade.symbol || !user) {
      toast({
        title: "Error",
        description: "Please enter a symbol first",
        variant: "destructive",
      })
      return
    }

    setIsUploading(true)

    try {
      // Upload image first
      const imageUrl = await uploadImage(file)

      // Create trade in database
      const tradeData = {
        symbol: newTrade.symbol.toUpperCase(),
        trade_date: newTrade.date,
        image_url: imageUrl,
      }

      let tradeId: string

      if (isSupabaseConfigured) {
        const result = await createTrade(user.id, tradeData)
        if (!result.success) {
          throw new Error(result.error)
        }
        tradeId = result.data.id
      } else {
        tradeId = Math.random().toString(36).substr(2, 9)
      }

      // Add to local state with analyzing flag
      const trade: Trade = {
        id: tradeId,
        symbol: tradeData.symbol,
        trade_date: tradeData.trade_date,
        image_url: imageUrl,
        isAnalyzing: true,
      }

      setTrades((prev) => [trade, ...prev])
      setNewTrade({ symbol: "", date: new Date().toISOString().split("T")[0] })

      toast({
        title: "Success",
        description: "Trade uploaded successfully. Analyzing...",
      })

      // Analyze image with AI
      const analysis = await analyzeImage(imageUrl)

      // Update trade with analysis
      if (isSupabaseConfigured) {
        await updateTradeAnalysis(tradeId, user.id, analysis)
      }

      setTrades((prev) => prev.map((t) => (t.id === tradeId ? { ...t, ai_analysis: analysis, isAnalyzing: false } : t)))
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        title: "Error",
        description: `Failed to upload image: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleEditTrade = async (tradeId: string, data: { symbol: string; trade_date: string }) => {
    if (!user) return

    try {
      if (isSupabaseConfigured) {
        const result = await updateTrade(tradeId, user.id, data)
        if (!result.success) {
          throw new Error(result.error)
        }
      }

      setTrades((prev) => prev.map((t) => (t.id === tradeId ? { ...t, ...data } : t)))

      toast({
        title: "Success",
        description: "Trade updated successfully",
      })
    } catch (error) {
      console.error("Error updating trade:", error)
      toast({
        title: "Error",
        description: `Failed to update trade: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    }
  }

  const handleDeleteTrade = async (tradeId: string) => {
    if (!user || !confirm("Are you sure you want to delete this trade?")) return

    try {
      if (isSupabaseConfigured) {
        const result = await deleteTrade(tradeId, user.id)
        if (!result.success) {
          throw new Error(result.error)
        }
      }

      setTrades((prev) => prev.filter((t) => t.id !== tradeId))

      toast({
        title: "Success",
        description: "Trade deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting trade:", error)
      toast({
        title: "Error",
        description: `Failed to delete trade: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    }
  }

  const handleShareTrade = async (tradeId: string) => {
    if (!user) return

    try {
      if (isSupabaseConfigured) {
        const result = await shareTrade(tradeId, user.id)
        if (!result.success) {
          throw new Error(result.error)
        }

        // Copy share URL to clipboard
        if (result.shareUrl) {
          await navigator.clipboard.writeText(result.shareUrl)
          toast({
            title: "Success",
            description: "Share link copied to clipboard!",
          })
        }

        setTrades((prev) =>
          prev.map((t) => (t.id === tradeId ? { ...t, is_shared: true, share_token: result.data.share_token } : t)),
        )
      } else {
        // Demo mode - generate a simple token
        const demoToken = `demo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const shareUrl = `${window.location.origin}/share/${demoToken}`
        await navigator.clipboard.writeText(shareUrl)

        setTrades((prev) => prev.map((t) => (t.id === tradeId ? { ...t, is_shared: true, share_token: demoToken } : t)))

        toast({
          title: "Success",
          description: "Share link copied to clipboard! (Demo mode)",
        })
      }
    } catch (error) {
      console.error("Error sharing trade:", error)
      toast({
        title: "Error",
        description: `Failed to share trade: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    }
  }

  const handleUnshareTrade = async (tradeId: string) => {
    if (!user) return

    try {
      if (isSupabaseConfigured) {
        const result = await unshareTrade(tradeId, user.id)
        if (!result.success) {
          throw new Error(result.error)
        }
      }

      setTrades((prev) => prev.map((t) => (t.id === tradeId ? { ...t, is_shared: false, share_token: undefined } : t)))

      toast({
        title: "Success",
        description: "Trade is no longer shared",
      })
    } catch (error) {
      console.error("Error unsharing trade:", error)
      toast({
        title: "Error",
        description: `Failed to unshare trade: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    }
  }

  const copyShareLink = async (shareToken: string) => {
    try {
      const shareUrl = `${window.location.origin}/share/${shareToken}`
      await navigator.clipboard.writeText(shareUrl)
      toast({
        title: "Success",
        description: "Share link copied to clipboard!",
      })
    } catch (error) {
      console.error("Error copying to clipboard:", error)
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard",
        variant: "destructive",
      })
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleImageUpload(file)
    }
  }

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      loadTrades(newPage)
    }
  }

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case "bullish":
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case "bearish":
        return <TrendingDown className="w-4 h-4 text-red-600" />
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-600" />
    }
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "bullish":
        return "bg-green-100 text-green-800"
      case "bearish":
        return "bg-red-100 text-red-800"
      default:
        return "bg-yellow-100 text-yellow-800"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <div className="space-y-6">
      {/* User Info */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold">Trades</h2>
          {user && <p className="text-sm text-gray-600">Logged in as: {user.email || "Demo User"}</p>}
          {isSupabaseConfigured && (
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-500">{setupMessage}</span>
              {isSettingUp && <Loader2 className="w-3 h-3 animate-spin" />}
              {!isSettingUp && setupMessage.includes("failed") && (
                <Button variant="ghost" size="sm" onClick={handleSetupStorage} className="h-6 px-2 text-xs">
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Retry Setup
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add New Trade */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Trade</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="symbol">Symbol</Label>
              <Input
                id="symbol"
                placeholder="e.g., AAPL"
                value={newTrade.symbol}
                onChange={(e) => setNewTrade((prev) => ({ ...prev, symbol: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={newTrade.date}
                onChange={(e) => setNewTrade((prev) => ({ ...prev, date: e.target.value }))}
              />
            </div>
          </div>

          <div>
            <Label>Upload Chart Screenshot</Label>
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              {isUploading ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Uploading and analyzing...</span>
                </div>
              ) : (
                <div className="flex flex-col items-center gap-2">
                  <Upload className="w-8 h-8 text-gray-400" />
                  <span className="text-sm text-gray-600">Click to upload chart screenshot</span>
                  <span className="text-xs text-gray-500">
                    {isSupabaseConfigured ? "Images will be stored in Supabase" : "Demo mode - images stored locally"}
                  </span>
                </div>
              )}
            </div>
            <input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileSelect} className="hidden" />
          </div>
        </CardContent>
      </Card>

      {/* Trades Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Recent Trades</CardTitle>
            <div className="text-sm text-gray-500">
              {pagination.total > 0 && (
                <>
                  Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} trades
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Chart</TableHead>
                <TableHead>Symbol</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Sentiment</TableHead>
                <TableHead>Confidence</TableHead>
                <TableHead>Recommendation</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TradeTableSkeleton />
              ) : trades.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                    No trades yet. Upload your first chart screenshot to get started!
                  </TableCell>
                </TableRow>
              ) : (
                trades.map((trade) => (
                  <TableRow key={trade.id}>
                    <TableCell>
                      {trade.image_url ? (
                        <img
                          src={trade.image_url || "/placeholder.svg"}
                          alt={`${trade.symbol} chart`}
                          className="w-16 h-12 object-cover rounded border"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/placeholder.svg?height=48&width=64"
                          }}
                        />
                      ) : (
                        <div className="w-16 h-12 bg-gray-100 rounded flex items-center justify-center">
                          <ImageIcon className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{trade.symbol}</TableCell>
                    <TableCell>{formatDate(trade.trade_date)}</TableCell>
                    <TableCell>
                      {trade.isAnalyzing ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="text-sm text-gray-600">Analyzing...</span>
                        </div>
                      ) : trade.ai_analysis ? (
                        <div className="flex items-center gap-2">
                          {getSentimentIcon(trade.ai_analysis.sentiment)}
                          <Badge className={getSentimentColor(trade.ai_analysis.sentiment)}>
                            {trade.ai_analysis.sentiment.toUpperCase()}
                          </Badge>
                        </div>
                      ) : (
                        <span className="text-gray-400">No analysis</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {trade.ai_analysis ? (
                        <span className="text-sm">{trade.ai_analysis.confidence}%</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {trade.ai_analysis ? (
                        <span className="text-sm text-gray-600 truncate max-w-xs block">
                          {trade.ai_analysis.recommendation}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {trade.is_shared ? (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          Shared
                        </Badge>
                      ) : (
                        <Badge variant="outline">Private</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" onClick={() => setSelectedTrade(trade)} className="gap-1">
                          <Eye className="w-4 h-4" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setEditingTrade(trade)}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />

                            {trade.is_shared ? (
                              <>
                                <DropdownMenuItem onClick={() => copyShareLink(trade.share_token!)}>
                                  <Copy className="w-4 h-4 mr-2" />
                                  Copy Link
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => window.open(`/share/${trade.share_token}`, "_blank")}>
                                  <ExternalLink className="w-4 h-4 mr-2" />
                                  View Shared
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUnshareTrade(trade.id)}>
                                  <Share className="w-4 h-4 mr-2" />
                                  Unshare
                                </DropdownMenuItem>
                              </>
                            ) : (
                              <DropdownMenuItem onClick={() => handleShareTrade(trade.id)}>
                                <Share className="w-4 h-4 mr-2" />
                                Share
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuSeparator />

                            <DropdownMenuItem onClick={() => handleDeleteTrade(trade.id)} className="text-red-600">
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Page {pagination.page} of {pagination.totalPages}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrev}
                  className="gap-1"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNext}
                  className="gap-1"
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Trade Detail Modal */}
      {selectedTrade && (
        <Card className="fixed inset-4 z-50 overflow-auto bg-white shadow-2xl">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>{selectedTrade.symbol} - Trade Details</CardTitle>
              <Button variant="ghost" onClick={() => setSelectedTrade(null)}>
                ✕
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Image */}
              <div>
                {selectedTrade.image_url ? (
                  <img
                    src={selectedTrade.image_url || "/placeholder.svg"}
                    alt={`${selectedTrade.symbol} chart`}
                    className="w-full h-64 object-cover rounded-lg border"
                  />
                ) : (
                  <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <ImageIcon className="w-16 h-16 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Analysis */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Trade Information</h3>
                  <p>
                    <strong>Symbol:</strong> {selectedTrade.symbol}
                  </p>
                  <p>
                    <strong>Date:</strong> {formatDate(selectedTrade.trade_date)}
                  </p>
                  <p>
                    <strong>Status:</strong>{" "}
                    {selectedTrade.is_shared ? (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        Shared
                      </Badge>
                    ) : (
                      <Badge variant="outline">Private</Badge>
                    )}
                  </p>
                </div>

                {selectedTrade.ai_analysis && (
                  <div>
                    <h3 className="font-semibold mb-2">AI Analysis</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        {getSentimentIcon(selectedTrade.ai_analysis.sentiment)}
                        <Badge className={getSentimentColor(selectedTrade.ai_analysis.sentiment)}>
                          {selectedTrade.ai_analysis.sentiment.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-600">
                          {selectedTrade.ai_analysis.confidence}% confidence
                        </span>
                      </div>

                      <div>
                        <h4 className="font-medium mb-1">Key Points:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {selectedTrade.ai_analysis.keyPoints.map((point, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                              {point}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-gray-50 p-3 rounded-lg">
                        <h4 className="font-medium mb-1">AI Recommendation:</h4>
                        <p className="text-sm text-gray-700">{selectedTrade.ai_analysis.recommendation}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit Trade Dialog */}
      <EditTradeDialog
        trade={editingTrade}
        open={!!editingTrade}
        onOpenChange={(open) => !open && setEditingTrade(null)}
        onSave={handleEditTrade}
      />
    </div>
  )
}
