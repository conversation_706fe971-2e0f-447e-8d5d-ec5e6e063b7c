"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { TrendingUp, TrendingDown, AlertCircle, ImageIcon, ExternalLink } from "lucide-react"

interface Trade {
  id: string
  symbol: string
  trade_date: string
  image_url?: string
  ai_analysis?: {
    sentiment: "bullish" | "bearish" | "neutral"
    confidence: number
    keyPoints: string[]
    recommendation: string
  }
  shared_at?: string
}

interface SharedTradeViewProps {
  trade: Trade
}

export function SharedTradeView({ trade }: SharedTradeViewProps) {
  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case "bullish":
        return <TrendingUp className="w-5 h-5 text-green-600" />
      case "bearish":
        return <TrendingDown className="w-5 h-5 text-red-600" />
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-600" />
    }
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "bullish":
        return "bg-green-100 text-green-800"
      case "bearish":
        return "bg-red-100 text-red-800"
      default:
        return "bg-yellow-100 text-yellow-800"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Shared Trade Analysis</h1>
          <p className="text-gray-600">
            {trade.symbol} • {formatDate(trade.trade_date)}
          </p>
          <div className="mt-4">
            <Button variant="outline" onClick={() => window.open("/", "_blank")} className="gap-2">
              <ExternalLink className="w-4 h-4" />
              Try TradingVue
            </Button>
          </div>
        </div>

        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Chart Image */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Chart Analysis</h3>
                {trade.image_url ? (
                  <img
                    src={trade.image_url || "/placeholder.svg"}
                    alt={`${trade.symbol} chart`}
                    className="w-full h-64 object-cover rounded-lg border shadow-sm"
                  />
                ) : (
                  <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <ImageIcon className="w-16 h-16 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Analysis */}
              <div>
                <h3 className="text-lg font-semibold mb-4">AI Analysis</h3>

                {trade.ai_analysis ? (
                  <div className="space-y-6">
                    {/* Sentiment */}
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        {getSentimentIcon(trade.ai_analysis.sentiment)}
                        <Badge className={`${getSentimentColor(trade.ai_analysis.sentiment)} text-sm px-3 py-1`}>
                          {trade.ai_analysis.sentiment.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-600">{trade.ai_analysis.confidence}% confidence</span>
                      </div>
                    </div>

                    {/* Key Points */}
                    <div>
                      <h4 className="font-medium mb-3 text-gray-900">Key Analysis Points</h4>
                      <ul className="space-y-2">
                        {trade.ai_analysis.keyPoints.map((point, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <span className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0" />
                            <span className="text-gray-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Recommendation */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-medium mb-2 text-gray-900">AI Recommendation</h4>
                      <p className="text-gray-700">{trade.ai_analysis.recommendation}</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">No AI analysis available for this trade.</div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>Shared on {trade.shared_at ? formatDate(trade.shared_at) : formatDate(trade.trade_date)}</p>
          <p className="mt-2">
            Powered by <span className="font-semibold text-teal-600">TradingVue</span>
          </p>
        </div>
      </div>
    </div>
  )
}
