"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, List, HelpCircle, GitCompare, Database, Sparkles } from "lucide-react"

interface QuickActionsProps {
  onAction: (action: string) => void
}

export function QuickActions({ onAction }: QuickActionsProps) {
  const actions = [
    {
      id: "summarize",
      icon: <FileText className="w-4 h-4" />,
      label: "Summarize",
      description: "Get a comprehensive summary",
    },
    {
      id: "keyPoints",
      icon: <List className="w-4 h-4" />,
      label: "Key Points",
      description: "Extract main insights",
    },
    {
      id: "questions",
      icon: <HelpCircle className="w-4 h-4" />,
      label: "Questions",
      description: "Generate relevant questions",
    },
    {
      id: "compare",
      icon: <GitCompare className="w-4 h-4" />,
      label: "Compare",
      description: "Compare multiple files",
    },
    {
      id: "extract",
      icon: <Database className="w-4 h-4" />,
      label: "Extract Data",
      description: "Pull out facts and figures",
    },
  ]

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Sparkles className="w-4 h-4 text-purple-500" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Quick Actions</span>
      </div>
      <div className="flex flex-wrap gap-2">
        {actions.map((action) => (
          <Button
            key={action.id}
            variant="outline"
            size="sm"
            onClick={() => onAction(action.id)}
            className="flex items-center gap-2 text-sm"
          >
            {action.icon}
            {action.label}
          </Button>
        ))}
      </div>
    </div>
  )
}
